// Jira-style time helpers

export function parseTimeToMinutes(input) {
  if (!input) return 0;
  const regex = /^(?:(\d+)\s*d)?\s*(?:(\d+)\s*h)?\s*(?:(\d+)\s*m)?$/i;
  const match = input.trim().match(regex);
  if (!match) return null;
  const days = parseInt(match[1] || 0, 10);
  const hours = parseInt(match[2] || 0, 10);
  const minutes = parseInt(match[3] || 0, 10);
  return days * 24 * 60 + hours * 60 + minutes;
}

export function isTimeFormatValid(input) {
  return /^(\d+\s*d)?\s*(\d+\s*h)?\s*(\d+\s*m)?$/i.test(input.trim());
}

export function formatMinutesToTime(totalMinutes) {
  if (!totalMinutes) return '';
  const d = Math.floor(totalMinutes / (24 * 60));
  const h = Math.floor((totalMinutes % (24 * 60)) / 60);
  const m = totalMinutes % 60;
  return [d ? `${d}d` : '', h ? `${h}h` : '', m ? `${m}m` : '']
    .filter(Boolean)
    .join(' ');
}
