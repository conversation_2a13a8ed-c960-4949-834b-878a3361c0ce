import React from 'react';
import Icon from '@/components/UI/AppIcon/AppIcon';
import NoDataView from '@/components/UI/NoDataView';
import CommonNoDataImage from '../../../CommonNoDataImage';
import './HaccpCard.scss';

const HaccpCard = ({ haccpData, highlightData = null }) => {
  const isHaccpHighlighted = !!highlightData?.haccp_attributes;

  const renderHaccpTitleWithHighlight = (instruction) => {
    if (isHaccpHighlighted && highlightData?.haccp_attributes) {
      // Find the corresponding old haccp data by matching attribute_title
      const oldHaccp = highlightData?.haccp_attributes?.find(
        (haccp) => haccp?.attribute_title === instruction?.attribute_title
      );

      // Check if there's a difference in the title
      if (
        oldHaccp &&
        oldHaccp?.attribute_title !== instruction?.attribute_title
      ) {
        return (
          <div className="highlight-container">
            <div
              className="haccp-card__haccp-title highlight-no-margin-bottom"
              dangerouslySetInnerHTML={{
                __html: instruction?.attribute_title || '',
              }}
            />
            <div
              className="highlight-original-text"
              dangerouslySetInnerHTML={{
                __html: oldHaccp?.attribute_title || '',
              }}
            />
          </div>
        );
      }
    }

    // Normal display
    return (
      <div
        className="haccp-card__haccp-title"
        dangerouslySetInnerHTML={{
          __html: instruction?.attribute_title || '',
        }}
      />
    );
  };

  // Helper function to render HACCP description with highlighting
  const renderHaccpDescriptionWithHighlight = (instruction) => {
    if (isHaccpHighlighted && highlightData?.haccp_attributes) {
      // Find the corresponding old haccp data by matching attribute_title
      const oldHaccp = highlightData?.haccp_attributes?.find(
        (haccp) => haccp?.attribute_title === instruction?.attribute_title
      );

      // Check if there's a difference in the description
      if (
        oldHaccp &&
        oldHaccp?.attribute_description !== instruction?.attribute_description
      ) {
        return (
          <div className="highlight-container">
            <div
              className="haccp-card__haccp-text highlight-no-margin-bottom"
              dangerouslySetInnerHTML={{
                __html: instruction?.attribute_description || '',
              }}
            />
            <div
              className="highlight-original-text"
              dangerouslySetInnerHTML={{
                __html: oldHaccp?.attribute_description || '',
              }}
            />
          </div>
        );
      }
    }

    // Normal display
    return (
      <div
        className="haccp-card__haccp-text"
        dangerouslySetInnerHTML={{
          __html: instruction?.attribute_description || '',
        }}
      />
    );
  };

  // Render all highlight data items
  const renderHighlightItems = () => {
    if (!isHaccpHighlighted || !highlightData?.haccp_attributes) return null;

    return highlightData.haccp_attributes.map((highlightItem, index) => (
      <div key={`highlight-${index}`} className="haccp-card__item">
        <div className="haccp-card__haccp-header">
          <div className="haccp-card__haccp-content">
            <div
              className="haccp-card__haccp-title highlight-original-text"
              dangerouslySetInnerHTML={{
                __html: highlightItem?.attribute_title || '',
              }}
            />
            <div
              className="haccp-card__haccp-text highlight-original-text"
              dangerouslySetInnerHTML={{
                __html: highlightItem?.attribute_description || '',
              }}
            />
          </div>
        </div>
      </div>
    ));
  };

  return (
    <div className="haccp-card">
      <div className="haccp-card__header">
        <p
          className={`haccp-card__title ${isHaccpHighlighted ? 'highlight-no-margin-bottom' : ''}`}
        >
          <Icon name="BadgeAlert" size={20} color="currentColor" />
          <span>HACCP</span>
        </p>
      </div>

      <div className="haccp-card__content">
        {!haccpData || haccpData?.length === 0 ? (
          <NoDataView
            image={<CommonNoDataImage />}
            title="No HACCP Available"
            description="There are no cooking HACCP available for this recipe at the moment."
            className="no-data-auto-height-conainer"
          />
        ) : (
          <div className="haccp-card__list">
            {/* Render current items */}
            {haccpData?.map((instruction, index) => (
              <div key={index} className="haccp-card__item">
                <div className="haccp-card__haccp-header">
                  <div className="haccp-card__haccp-content">
                    {renderHaccpTitleWithHighlight(instruction)}
                    {renderHaccpDescriptionWithHighlight(instruction)}
                  </div>
                </div>
              </div>
            ))}

            {/* Render all highlight data items */}
            {renderHighlightItems()}
          </div>
        )}
      </div>
    </div>
  );
};

export default HaccpCard;
