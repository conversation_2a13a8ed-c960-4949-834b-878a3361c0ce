'use client';

import React from 'react';
import { Tooltip, Typography } from '@mui/material';
import Icon from '@/components/UI/AppIcon/AppIcon';
import { getFormattedTotalTime } from '@/helper/common/commonFunctions';
import './SectionNavigationSidebar.scss';

export default function SectionNavigationSidebar({
  formSections,
  activeSection,
  onSectionChange,
  validationErrors,
  calculations,
  currency,
  isMobileView,
}) {
  // Don't render on mobile
  if (isMobileView) {
    return null;
  }

  return (
    <div className="section-navigation-sidebar">
      <div className="section-navigation-sidebar__content">
        <h2 className="section-navigation-sidebar__title">Recipe Sections</h2>

        <div className="section-navigation-sidebar__nav">
          {formSections?.map((section, index) => {
            const isDisabled = section?.isDisabled;
            const prevSection = formSections[index - 1];
            const prevSectionTitle = prevSection?.title || 'previous section';

            const tooltipTitle = isDisabled
              ? `Please complete ${prevSectionTitle} first`
              : '';

            const sectionButton = (
              <button
                key={section?.id}
                onClick={() => onSectionChange(section?.id)}
                className={`section-navigation-sidebar__button ${
                  activeSection === section?.id
                    ? 'section-navigation-sidebar__button--active'
                    : ''
                } ${
                  isDisabled
                    ? 'section-navigation-sidebar__button--disabled'
                    : ''
                }`}
                disabled={isDisabled}
              >
                <Icon
                  name={section?.icon}
                  size={20}
                  color={
                    activeSection === section?.id ? 'white' : 'currentColor'
                  }
                />
                <div className="section-navigation-sidebar__section-content">
                  <div className="section-navigation-sidebar__section-title">
                    {section?.title}
                    {section?.required && (
                      <span className="section-navigation-sidebar__required">
                        *
                      </span>
                    )}
                  </div>
                </div>
                {validationErrors[section?.id] && (
                  <Icon name="AlertCircle" size={16} color="#EF4444" />
                )}
              </button>
            );

            return isDisabled ? (
              <Tooltip
                key={section?.id}
                title={
                  <Typography className="sub-title-text">
                    {tooltipTitle}
                  </Typography>
                }
                arrow
                classes={{
                  tooltip: 'info-tooltip-container',
                }}
              >
                <span>{sectionButton}</span>
              </Tooltip>
            ) : (
              sectionButton
            );
          })}
        </div>

        {/* Cost Summary */}
        <div className="section-navigation-sidebar__cost-summary">
          <h3 className="section-navigation-sidebar__cost-title">
            Cost Summary
          </h3>
          <div className="section-navigation-sidebar__cost-items">
            <div className="section-navigation-sidebar__cost-item">
              <span className="section-navigation-sidebar__cost-label">
                Total Cost:
              </span>
              <span className="section-navigation-sidebar__cost-value">
                {currency}
                {calculations.totalCost.toFixed(2)}
              </span>
            </div>
            <div className="section-navigation-sidebar__cost-item">
              <span className="section-navigation-sidebar__cost-label">
                Per Portion:
              </span>
              <span className="section-navigation-sidebar__cost-value">
                {currency}
                {calculations.portionCost.toFixed(2)}
              </span>
            </div>
            <div className="section-navigation-sidebar__cost-item">
              <span className="section-navigation-sidebar__cost-label">
                Total Time:
              </span>
              <span className="section-navigation-sidebar__cost-value">
                {/* {Math.round(calculations.totalTime)} min */}
                {getFormattedTotalTime(calculations.totalTime)}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
