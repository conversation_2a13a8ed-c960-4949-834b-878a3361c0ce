import React, { useState, useEffect, useRef } from 'react';
import { EmptyConversationIcon } from '@/helper/common/images';
import { Box, Typography } from '@mui/material';
import {
  DateFormat,
  checkOrganizationRole,
  setApiMessage,
} from '@/helper/common/commonFunctions';
import { fetchFromStorage } from '@/helper/context';
import { identifiers } from '@/helper/constants/identifier';
import { supportTicketService } from '@/services/supportTicketService';
import SendIcon from '@mui/icons-material/Send';
import VisibilityIcon from '@mui/icons-material/Visibility';
import VisibilityOffIcon from '@mui/icons-material/VisibilityOff';
import InsertDriveFileIcon from '@mui/icons-material/InsertDriveFile';
import RemoveRedEyeIcon from '@mui/icons-material/RemoveRedEye';
import DownloadIcon from '@mui/icons-material/Download';
import HeaderImage from '@/components/UI/ImageSecurity';
import CustomTextField from '@/components/UI/CustomTextField';
import CustomCheckbox from '@/components/UI/CustomCheckbox';
import CustomButton from '@/components/UI/CustomButton';
import ContentLoader from '@/components/UI/ContentLoader';
import './conversation.scss';

export default function Conversation({ ticketId, ticket }) {
  const [newMessage, setNewMessage] = useState('');
  const [isInternalNote, setIsInternalNote] = useState(
    checkOrganizationRole('super_admin')
  );
  const [conversationData, setConversationData] = useState({
    data: [],
    pagination: null,
    totalCount: 0,
  });
  const [loading, setLoading] = useState(false);
  const [sendingMessage, setSendingMessage] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const conversationRef = useRef(null);

  // Get current user ID from storage
  const getCurrentUserId = () => {
    const authData = fetchFromStorage(identifiers.AUTH_DATA);
    return authData?.user?.id || authData?.id || authData?.user_id;
  };

  // Check if user is super_admin to show internal note checkbox
  const isSuperAdmin = checkOrganizationRole('super_admin');
  // Check if user is assigned to this ticket (assigned user can see internal notes)
  const isAssignedUser =
    ticket?.assigned_to_user_id &&
    getCurrentUserId() === ticket?.assigned_to_user_id;

  // Common message input component to avoid all duplication
  const renderMessageInput = () => (
    <>
      {/* Internal note checkbox - visible for super admin and assigned users */}
      {(isSuperAdmin || isAssignedUser) && (
        <Box className="internal-note-checkbox d-flex align-center">
          <CustomCheckbox
            checked={isInternalNote}
            onChange={(e) => setIsInternalNote(e?.target?.checked)}
            name="internal_note"
          />
          <Box className="d-flex align-center gap-sm">
            {isInternalNote ? (
              <VisibilityIcon className="visibility-icon active" />
            ) : (
              <VisibilityOffIcon className="visibility-icon" />
            )}
            <Typography className="checkbox-label body-sm">
              Internal note (visible to agents only)
            </Typography>
          </Box>
        </Box>
      )}

      {/* Message input container */}
      <Box className="message-input-container">
        <Box className="conversation-input-wrapper d-flex align-center gap-sm">
          <Box className="conversation-input-container">
            <CustomTextField
              className="conversation-input"
              fullWidth
              placeholder="Add a comment..."
              value={newMessage}
              onChange={(e) => setNewMessage(e?.target?.value)}
              onKeyDown={handleKeyDown}
              variant="outlined"
              maxRows={3}
              minRows={3}
              multiline
              disabled={sendingMessage}
            />
          </Box>
          <Box
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              if (!newMessage?.trim() || sendingMessage) return;
              handleSendMessage(e);
            }}
            style={{
              cursor:
                !newMessage?.trim() || sendingMessage
                  ? 'not-allowed'
                  : 'pointer',
              opacity: !newMessage?.trim() || sendingMessage ? 0.5 : 1,
            }}
          >
            <CustomButton
              type="button"
              startIcon={<SendIcon />}
              isIconOnly
              disabled={!newMessage?.trim() || sendingMessage}
              className="conversation-send-button-icon"
              variant="outlined"
              style={{ pointerEvents: 'none' }}
            />
          </Box>
        </Box>
      </Box>
    </>
  );

  // Smart function to determine sender display name based on message data and user roles
  const getSenderDisplayName = (message) => {
    const currentUserId = getCurrentUserId();
    const messageCreatedBy = message?.created_by;
    const messageType = message?.message_type;
    const senderName = message?.sender_name;
    const isPrivate =
      message?.is_private || message?.include_private || message?.private;

    // Check user roles within function scope
    const isCurrentUserOrgMaster = checkOrganizationRole('org_master');
    const isCurrentUserSuperAdmin = checkOrganizationRole('super_admin');

    // If current user sent this message, always show "You"
    if (messageCreatedBy === currentUserId) {
      return 'You';
    }

    // AGENT type messages (Super Admin messages)
    if (messageType === 'AGENT') {
      if (isPrivate) {
        // For internal notes, show sender name to super_admin and assigned users
        if (
          isCurrentUserSuperAdmin ||
          (ticket?.assigned_to_user_id &&
            messageCreatedBy !== currentUserId &&
            ticket?.assigned_to_user_id === currentUserId)
        ) {
          return senderName;
        }
      }
      return 'Support Team'; // Everyone else sees Support Team
    }

    // USER type messages (Org Master or Assigned User messages)
    if (messageType === 'USER') {
      // Super admin viewing USER message - always sees actual name
      if (isCurrentUserSuperAdmin) {
        return senderName;
      }

      // Org master viewing USER message
      if (isCurrentUserOrgMaster) {
        // Check if sender is the ticket creator (org_master)
        const isFromTicketCreator = messageCreatedBy === ticket?.creator_id;
        if (isFromTicketCreator) {
          return senderName; // Show actual name for ticket creator
        } else {
          return 'Support Team'; // Show Support Team for assigned user messages
        }
      }

      // Assigned user or other roles viewing USER message
      return senderName;
    }

    // Fallback
    return senderName || 'User';
  };

  // Fetch conversation data when ticketId changes
  useEffect(() => {
    if (ticketId) {
      fetchConversationData(1, true);
    }
  }, [ticketId]);

  // Add scroll event listener for infinite scroll
  useEffect(() => {
    const conversationContainer = conversationRef.current;
    if (conversationContainer) {
      conversationContainer.addEventListener('scroll', handleScroll);
      return () => {
        conversationContainer.removeEventListener('scroll', handleScroll);
      };
    }
  }, [hasMore, loading, currentPage]); // Dependencies for the scroll handler

  // Scroll to bottom when conversation first loads
  useEffect(() => {
    if (conversationData?.data?.length > 0 && currentPage === 1) {
      const container = conversationRef.current;
      if (container) {
        setTimeout(() => {
          container.scrollTop = container.scrollHeight;
        }, 100);
      }
    }
  }, [conversationData, currentPage]);

  // Function to load more messages
  const loadMoreMessages = () => {
    if (hasMore && !loading) {
      const nextPage = currentPage + 1;
      setCurrentPage(nextPage);
      fetchConversationData(nextPage, false);
    }
  };

  // Scroll handler for infinite scroll - load older messages when scrolling to top
  const handleScroll = (e) => {
    const { scrollTop } = e?.target || {};
    // Load more when user scrolls near the top (within 100px)
    if (scrollTop <= 100 && hasMore && !loading) {
      console.log(
        'Loading more messages - scrollTop:',
        scrollTop,
        'hasMore:',
        hasMore,
        'loading:',
        loading
      );
      loadMoreMessages();
    }
  };

  const fetchConversationData = async (page = 1, reset = false) => {
    if (!ticketId) return;

    setLoading(true);
    try {
      // Logic for include_private:
      // - super_admin: true (can see internal notes)
      // - assigned user: true (can see internal notes)
      // - org_master: false (cannot see internal notes)
      // - staff: false (cannot see internal notes)
      const shouldIncludePrivate = isSuperAdmin || isAssignedUser;

      const params = {
        include_private: shouldIncludePrivate,
        page: page,
        limit: 10,
      };

      const response = await supportTicketService.getTicketConversation(
        ticketId,
        params
      );

      console.log('=== API Response Debug ===');
      console.log('Full API Response:', response);
      console.log('Response data:', response?.data);
      console.log('Response data type:', typeof response?.data);
      console.log('Response data is array:', Array.isArray(response?.data));
      console.log('Response data length:', response?.data?.length);
      console.log('Response pagination:', response?.pagination);
      console.log('Response totalCount:', response?.totalCount);
      console.log('Current page:', page);
      console.log('Should include private:', shouldIncludePrivate);
      console.log('=== End API Response Debug ===');

      if (response?.data && Array.isArray(response.data)) {
        if (reset || page === 1) {
          setConversationData(response);
        } else {
          // For pagination, prepend older messages to the beginning
          setConversationData((prev) => ({
            ...response,
            data: [...(response?.data || []), ...(prev?.data || [])],
          }));
        }

        setCurrentPage(page);
        // Check if there are more pages available
        const totalPages =
          response.pagination?.totalPages ||
          Math.ceil((response.totalCount || 0) / 10);
        setHasMore(page < totalPages);
      } else {
        if (reset || page === 1) {
          setConversationData({ data: [], pagination: null, totalCount: 0 });
        }
        setHasMore(false);
      }
    } catch (error) {
      console.error('Error fetching conversation:', error);
      setApiMessage(
        'error',
        error?.response?.data?.message || 'Failed to fetch conversation'
      );
      if (reset || page === 1) {
        setConversationData({ data: [], pagination: null, totalCount: 0 });
      }
    } finally {
      setLoading(false);
    }
  };

  // Filter out internal notes based on user role:
  // - super_admin and assigned users can see internal notes with sender names
  // - org_master and staff cannot see internal notes at all
  const canSeeInternalNotes = isSuperAdmin || isAssignedUser;
  const filteredConversationData = canSeeInternalNotes
    ? conversationData?.data || []
    : (conversationData?.data || []).filter((message) => !message?.is_private);

  console.log('=== State Debug ===');
  console.log('Conversation data:', conversationData);
  console.log('Conversation data.data:', conversationData?.data);
  console.log('Conversation data.data type:', typeof conversationData?.data);
  console.log(
    'Conversation data.data is array:',
    Array.isArray(conversationData?.data)
  );
  console.log('Can see internal notes:', canSeeInternalNotes);
  console.log('Filtered conversation data:', filteredConversationData);
  console.log('Filtered data length:', filteredConversationData?.length);
  console.log('=== End State Debug ===');

  const handleSendMessage = async (e) => {
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }
    if (!newMessage?.trim() || !ticketId || sendingMessage) return;

    const messageText = newMessage?.trim();
    setNewMessage(''); // Clear input immediately for better UX
    setSendingMessage(true);

    try {
      const messageData = {
        message_text: messageText,
        is_private: isInternalNote,
        message_type: isSuperAdmin || isAssignedUser ? 'AGENT' : 'USER',
      };

      const response = await supportTicketService.addTicketMessage(
        ticketId,
        messageData
      );

      if (response) {
        // Add the new message immediately to show it right away
        if (response?.data) {
          setConversationData((prev) => ({
            ...prev,
            data: [...(prev?.data || []), response?.data],
          }));

          // Scroll to bottom after sending message
          setTimeout(() => {
            const container = conversationRef.current;
            if (container) {
              container.scrollTop = container.scrollHeight;
            }
          }, 100);
        }

        // Show the success message from API response
        setApiMessage(
          'success',
          response?.message || 'Message sent successfully'
        );

        // No need to refresh conversation data - we already added the new message above
      }
    } catch (error) {
      console.error('Error sending message:', error);
      setApiMessage(
        'error',
        error?.response?.data?.message || 'Failed to send message'
      );
      // Restore the message text if sending failed
      setNewMessage(messageText);
    } finally {
      setSendingMessage(false);
    }
  };

  const handleKeyDown = (e) => {
    if (e?.key === 'Enter' && !e?.shiftKey && !sendingMessage) {
      e.preventDefault();
      handleSendMessage(e);
    }
  };

  // Handle download for attachments
  const handleDownload = async (fileUrl, fileName) => {
    try {
      if (!fileUrl || !fileName) {
        console.error('Invalid file URL or filename');
        return;
      }

      if (fileUrl.startsWith('http')) {
        const response = await fetch(fileUrl);
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);

        const link = document.createElement('a');
        link.href = url;
        link.download = fileName;
        link.style.display = 'none';

        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        window.URL.revokeObjectURL(url);
      } else {
        const link = document.createElement('a');
        link.href = fileUrl;
        link.download = fileName;
        link.style.display = 'none';

        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      }
    } catch (error) {
      console.error('Download failed:', error);
      window.open(fileUrl, '_blank');
    }
  };

  // Render attachments using Change Request UI style
  const renderAttachments = (attachments) => {
    if (!attachments || attachments.length === 0) return null;

    return (
      <Box className="message-attachments pt8">
        <Typography className="body-text fw500 pb4">Attached files</Typography>
        <Box className="file-grid-container">
          {attachments.map((attachment, index) => {
            const fileName =
              attachment?.attachment_name ||
              attachment?.item_name ||
              attachment?.name ||
              'Unknown file';
            const fileUrl =
              attachment?.download_url ||
              attachment?.url ||
              attachment?.preview;
            const mimeType =
              attachment?.mime_type ||
              attachment?.item_mime_type ||
              attachment?.type ||
              '';
            const isImage = mimeType.startsWith('image/');

            return (
              <Box
                key={index}
                className="selected-files selected-view-files file-grid-item"
              >
                <Box className="file-name">
                  <Box className="d-flex align-center gap-sm">
                    <InsertDriveFileIcon className="file-icon" />
                    <Typography className="title-text text-ellipsis-line text-capital">
                      {fileName}
                    </Typography>
                  </Box>
                </Box>
                {isImage && fileUrl && (
                  <HeaderImage
                    type="url"
                    imageUrl={fileUrl}
                    Content={<RemoveRedEyeIcon />}
                    className="d-flex align-center"
                  />
                )}
                <DownloadIcon
                  className="ml8"
                  onClick={() => handleDownload(fileUrl, fileName)}
                  sx={{ cursor: 'pointer' }}
                />
              </Box>
            );
          })}
        </Box>
      </Box>
    );
  };

  if (loading) {
    return (
      <Box className="convesation-wrap d-flex flex-col align-center justify-center text-align pb32">
        <ContentLoader />
      </Box>
    );
  }

  if (!filteredConversationData?.length) {
    return (
      <Box className="conversation-container">
        <Box className="convesation-wrap d-flex flex-col align-center justify-center text-align pb32">
          <EmptyConversationIcon className="conversation-icon" />
          <Box>
            <Typography className="conversation-text body-sm">
              No Conversation available
            </Typography>
            {/* Debug info */}
            <Typography
              className="body-xs"
              style={{ color: 'red', marginTop: '10px' }}
            >
              Debug: Raw data length: {conversationData?.data?.length || 0},
              Filtered length: {filteredConversationData?.length || 0}, Can see
              internal: {canSeeInternalNotes ? 'Yes' : 'No'}
            </Typography>
          </Box>
        </Box>
        {/* Message input for org_master users even when no conversation exists */}
        {/* {(!isStaff || isOrgMaster || isSuperAdmin) &&  */}
        {/* } */}
        {renderMessageInput()}
      </Box>
    );
  }

  return (
    <Box className="conversation-container">
      <Box className="conversation-messages" ref={conversationRef}>
        {/* Loading indicator for loading older messages at the top */}
        {loading && hasMore && currentPage > 1 && (
          <Box className="load-more-container d-flex justify-center mb16">
            <ContentLoader />
          </Box>
        )}

        {filteredConversationData?.map((message) => (
          <Box
            key={message?.id}
            className={`message-item ${message?.is_private || message?.include_private || message?.private ? 'internal-note' : ''}`}
          >
            <Box className="message-header">
              <Typography className="sender-name text-capital body-sm">
                {getSenderDisplayName(message)}
                {(message?.is_private ||
                  message?.include_private ||
                  message?.private) && (
                  <span className="internal-note-badge sub-title-text">
                    Internal Note
                  </span>
                )}
              </Typography>
              <Typography className="message-timestamp body-xs">
                {DateFormat(message?.created_at, 'datesWithhour')}
              </Typography>
            </Box>
            <Box className="message-content">
              <Typography className="message-text body-sm">
                {message?.message_text}
              </Typography>

              {/* Render attachments using Change Request UI */}
              {renderAttachments(message?.attachments || message?.files || [])}
            </Box>
          </Box>
        ))}

        {/* Loading indicator for initial load */}
        {loading && currentPage === 1 && (
          <Box className="load-more-container d-flex justify-center mt16">
            <ContentLoader />
          </Box>
        )}
      </Box>
      {/* Message input - show for org_master, super_admin, and non-staff users */}
      {/* {(!isStaff || isOrgMaster || isSuperAdmin) &&  */}
      {renderMessageInput()}
      {/* } */}
    </Box>
  );
}
