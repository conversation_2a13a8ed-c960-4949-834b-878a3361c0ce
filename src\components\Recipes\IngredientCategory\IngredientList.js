'use client';
import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Tooltip,
  Divider,
  gridClasses,
  useTheme,
  useMediaQuery,
} from '@mui/material';
import { DataGrid } from '@mui/x-data-grid';
import { ArrowUpward, ArrowDownward } from '@mui/icons-material';
import { fetchFromStorage, saveToStorage } from '@/helper/context';
import {
  DateFormat,
  IngredientIconSize,
  checkOrganizationRole,
  setApiMessage,
} from '@/helper/common/commonFunctions';

import { identifiers } from '@/helper/constants/identifier';
import { staticOptions } from '@/helper/common/staticOptions';
import CommonNoDataImage from '../Recipes/CommonNoDataImage';
import EditIcon from '@/components/ActionIcons/EditIcon';
import DeleteIcon from '@/components/ActionIcons/DeleteIcon';
import CustomSearch from '@/components/UI/CustomSearch';
import CustomButton from '@/components/UI/CustomButton';
import DialogBox from '@/components/UI/Modalbox';
import DeleteModal from '@/components/UI/DeleteModal';

import NoDataView from '@/components/UI/NoDataView';
import CustomSelect from '@/components/UI/CustomSelect';
import RecipesIcon from '@/components/UI/RecipePlaceholderIcon/RecipesIcon';
import ClearOutlinedIcon from '@mui/icons-material/ClearOutlined';
import CheckIcon from '@mui/icons-material/Check';
import AddIcon from '@mui/icons-material/Add';

import CustomOrgPagination from '@/components/UI/customPagination';
import FilterListIcon from '@mui/icons-material/FilterList';
import RightDrawer from '@/components/UI/RightDrawer';
import FilterComponent from '@/components/UI/FilterComponent';
import AddEditIngredientCat from './AddEditIngredientCat';

import {
  getIngredientList,
  deleteIngredientCategory,
} from '@/services/recipeService';
import ContentLoader from '@/components/UI/ContentLoader';
import './ingredientlist.scss';

const organizationOptions = [
  { value: 'org1', label: 'Organization 1' },
  { value: 'org2', label: 'Organization 2' },
  { value: 'org3', label: 'Organization 3' },
];

const createSortableHeader = (field, label, sortOrder, onSort) => (
  <Box className="d-flex align-center gap-5">
    <Box className="wrap-header-text d-flex align-center">
      <Typography className="title-text fw600">{label}</Typography>
      <Box className="amount-text arrow-wrap">
        {sortOrder?.key === field && sortOrder?.value === 'DESC' ? (
          <ArrowDownward
            className="arrow-icon cursor-pointer"
            fontSize="small"
            onClick={() => onSort(field)}
          />
        ) : (
          <ArrowUpward
            className="arrow-icon cursor-pointer"
            fontSize="small"
            onClick={() => onSort(field)}
          />
        )}
      </Box>
    </Box>
  </Box>
);

const createColumns = (
  sortOrder,
  handleSort,
  handleAddEdit,
  handleDelete,
  currentPage,
  rowsPerPage,
  paginatedData
) => [
  {
    field: 'id',
    headerName: 'No.',
    width: 48,
    minWidth: 48,
    sortable: false,
    flex: 0,
    headerAlign: 'start',
    align: 'start',
    renderCell: (params) => {
      // Find the index of this row in the paginated data
      const rowIndex = paginatedData.findIndex(
        (row) => row?.id === params?.row?.id
      );
      const sequentialNumber = (currentPage - 1) * rowsPerPage + rowIndex + 1;
      return (
        <Typography className="text-ellipsis">{sequentialNumber}</Typography>
      );
    },
  },
  {
    field: 'name',
    headerName: 'Name',
    width: 250,
    minWidth: 250,
    flex: 1,
    sortable: false,
    headerAlign: 'start',
    align: 'start',
    renderHeader: () =>
      createSortableHeader('name', 'Name', sortOrder, handleSort),
    renderCell: (params) => {
      return (
        <Box className="h100 d-flex">
          <Box className="d-flex align-center gap-5">
            {params?.row?.iconItem?.iconUrl && (
              <Box className="d-flex">
                <RecipesIcon
                  iconUrl={params?.row?.iconItem?.iconUrl}
                  altText={params?.row?.category_name}
                  imgWidth={IngredientIconSize}
                  imgHeight={IngredientIconSize}
                />
              </Box>
            )}
            {params?.row?.category_description ? (
              <Tooltip
                title={
                  <Typography className="sub-title-text">
                    {params?.row?.category_description}
                  </Typography>
                }
                arrow
                classes={{
                  tooltip: 'info-tooltip-container',
                }}
              >
                <Typography className="title-text">
                  {params?.row?.category_name}
                </Typography>
              </Tooltip>
            ) : (
              <Typography className="title-text">
                {params?.row?.category_name}
              </Typography>
            )}
            <Tooltip
              title={
                <>
                  {params?.row?.is_system_category && (
                    <Typography className="sub-title-text">Default</Typography>
                  )}
                </>
              }
              arrow
              classes={{
                tooltip: 'info-tooltip-container',
              }}
            >
              {params?.row?.is_system_category && (
                <Typography className="default-d-text content-text fw600">
                  D
                </Typography>
              )}
            </Tooltip>
          </Box>
        </Box>
      );
    },
  },
  {
    field: 'status',
    headerName: 'Status',
    width: 120,
    minWidth: 120,
    flex: 1,
    sortable: false,
    headerAlign: 'start',
    align: 'start',
    renderHeader: () =>
      createSortableHeader('status', 'Status', sortOrder, handleSort),
    renderCell: (params) => {
      return (
        <Box className={'title-text h100 d-flex align-center justify-center '}>
          <Typography
            className={`sub-title-text fw600 ${
              params?.row?.category_status === 'active'
                ? 'label-active'
                : params?.row?.category_status === 'inactive'
                  ? 'failed'
                  : ''
            }`}
          >
            {params?.row?.category_status}
          </Typography>
        </Box>
      );
    },
  },
  {
    field: 'actionBy',
    headerName: 'Action By',
    width: 180,
    minWidth: 180,
    flex: 1,
    sortable: false,
    headerAlign: 'start',
    align: 'start',
    renderCell: (params) => (
      // <Box className="gap-5 h100 d-flex align-center">
      //   <Box className="text-ellipsis">
      //     <Typography className="title-text">
      //       {params?.row?.updated_by_name}
      //     </Typography>
      //     <Typography className="title-text">
      //       {DateFormat(params?.row?.updated_at, 'datesWithhour')}
      //     </Typography>
      //   </Box>
      // </Box>

      <Box className="gap-5 h100 d-flex align-center">
        <Box className="text-ellipsis">
          <Typography className="title-text">
            {params?.row?.is_system_category
              ? 'System Generated'
              : params?.row?.updated_by_name}
          </Typography>
          <Typography
            className={`title-text ${params?.row?.is_system_category ? 'system-generated-text' : ''}`}
          >
            {DateFormat(params?.row?.updated_at, 'datesWithhour')}
          </Typography>
        </Box>
      </Box>
    ),
  },
  {
    field: 'actions',
    headerName: 'Actions',
    width: 120,
    minWidth: 120,
    flex: 1,
    sortable: false,
    headerAlign: 'center',
    align: 'center',
    renderCell: (params) => (
      <Box className="d-flex actions align-center justify-center h100">
        {!params?.row?.is_system_category ? (
          <>
            <Tooltip
              title={<Typography className="sub-title-text">Edit</Typography>}
              arrow
              classes={{
                tooltip: 'info-tooltip-container',
              }}
            >
              <Box
                className="d-flex"
                onClick={() => handleAddEdit(params?.row)}
              >
                <EditIcon />
              </Box>
            </Tooltip>
            <Tooltip
              title={<Typography className="sub-title-text">Delete</Typography>}
              arrow
              classes={{
                tooltip: 'info-tooltip-container',
              }}
            >
              <Box className="d-flex">
                <DeleteIcon onClick={() => handleDelete(params?.row?.id)} />
              </Box>
            </Tooltip>
          </>
        ) : (
          '-'
        )}
      </Box>
    ),
  },
];

export default function IngredientList() {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down(1500));

  const [currentPage, setCurrentPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [searchValue, setSearchValue] = useState('');
  const [sortOrder, setSortOrder] = useState({ key: '', value: 'ASC' });
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [deleteId, setDeleteId] = useState(null);
  const [filteredIngredients, setFilteredIngredients] = useState([]);
  const [isTableAdjusting, setIsTableAdjusting] = useState(false);
  const [totalCount, setTotalCount] = useState(0);
  const [openFilterDrawer, setOpenFilterDrawer] = useState(false);
  const [selectedFilters, setSelectedFilters] = useState([]);
  const [filterData, setFilterData] = useState({
    status: '',
    type: '',
    organizationId: '',
  });
  const [filterDataApplied, setFilterDataApplied] = useState({
    status: '',
    type: '',
    organizationId: '',
    searchValue: '',
  });
  const [addEditModal, setAddEditModal] = useState(false);
  const [singleData, setSingleData] = useState('');

  const filters = [
    {
      key: 'search',
      label: 'Search',
      options: [],
      permission: true,
    },
    {
      key: 'status',
      label: 'Status',
      options: staticOptions?.ORG_STATUS,
      permission: true,
    },
    {
      key: 'type',
      label: 'Type',
      options: staticOptions?.INGREDIENT_TYPE,
      permission: true,
    },
    {
      key: 'organizationId',
      label: 'Organization',
      options: organizationOptions,
      permission: checkOrganizationRole('super_admin'),
    },
  ];

  const getIngredientListData = async (search, page, filter, Rpp, Sort) => {
    try {
      const { ingredients, totalCount } = await getIngredientList(
        search,
        page,
        filter,
        Rpp,
        Sort
      );
      setFilteredIngredients(ingredients);
      setTotalCount(totalCount);
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
      // Clear data on error since we're using API-only approach
      setFilteredIngredients([]);
      setTotalCount(0);
    }
  };

  const toggleFilter = (key) => {
    setSelectedFilters((prevFilters) => {
      if (prevFilters?.includes(key)) {
        return prevFilters?.filter((item) => item !== key);
      } else {
        const index = filters.findIndex((filter) => filter.key === key);
        const newFilters = [...prevFilters];
        newFilters.splice(index, 0, key);
        return newFilters;
      }
    });
  };

  const getFirstFourFilters = () => {
    const savedFilters = fetchFromStorage(identifiers?.INGREDIENT_CAT_FILTER);
    setSelectedFilters(savedFilters?.slice(0, 4));
    saveToStorage(
      identifiers?.INGREDIENT_CAT_FILTER,
      savedFilters?.slice(0, 4)
    );
  };

  const saveLayout = () => {
    saveToStorage(identifiers?.INGREDIENT_CAT_FILTER, selectedFilters);
    setOpenFilterDrawer(false);
  };

  useEffect(() => {
    const savedFilters = fetchFromStorage(identifiers?.INGREDIENT_CAT_FILTER);
    if (!savedFilters) {
      setSelectedFilters(filters.slice(0, 4).map((filter) => filter.key));
    } else {
      setSelectedFilters(savedFilters);
    }
  }, []);

  // Load initial data on component mount
  useEffect(() => {
    getIngredientListData('', 1, filterDataApplied, rowsPerPage, sortOrder);
  }, []);

  // Filter options

  const handleDelete = (id) => {
    setDeleteDialogOpen(true);
    setDeleteId(id);
  };

  const handleConfirmDelete = async () => {
    try {
      // Call delete API using service function
      const response = await deleteIngredientCategory(deleteId);

      // Show success message from API or fallback
      setApiMessage('success', response?.message);

      // Refresh the list after deletion
      await getIngredientListData(
        filterDataApplied.searchValue || '',
        currentPage,
        filterDataApplied,
        rowsPerPage,
        sortOrder
      );
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    }
    handleCloseDeleteDialog();
  };

  const handleCloseDeleteDialog = () => {
    setDeleteDialogOpen(false);
    setDeleteId(null);
  };

  const handleSort = async (key) => {
    const newOrder = sortOrder?.value === 'ASC' ? 'DESC' : 'ASC';
    const newSortOrder = { key, value: newOrder };
    setSortOrder(newSortOrder);
    setCurrentPage(1);

    // Call API with new sort order
    await getIngredientListData(
      filterDataApplied.searchValue || '',
      1,
      filterDataApplied,
      rowsPerPage,
      newSortOrder
    );
  };

  const handleSearch = async () => {
    setIsTableAdjusting(true);
    try {
      // Call API with current search and filter values
      await getIngredientListData(
        searchValue,
        1, // Reset to first page
        filterDataApplied,
        rowsPerPage,
        sortOrder
      );
      // Apply current search value to filterDataApplied
      setFilterDataApplied((prev) => ({
        ...prev,
        searchValue: searchValue,
      }));
      setCurrentPage(1);
    } finally {
      // isTableAdjusting will be set to false in the useMemo hook
      setTimeout(() => {
        setIsTableAdjusting(false);
      }, 100);
    }
  };

  const handleClearSearch = async () => {
    setIsTableAdjusting(true);
    setSearchValue('');
    setFilterData({
      status: '',
      type: '',
      organizationId: '',
    });
    setFilterDataApplied({
      status: '',
      type: '',
      organizationId: '',
      searchValue: '', // Clear applied search value too
    });
    setCurrentPage(1);

    // Call API with cleared filters
    try {
      await getIngredientListData(
        '',
        1,
        {
          status: '',
          type: '',
          organizationId: '',
          searchValue: '',
        },
        rowsPerPage,
        sortOrder
      );
    } finally {
      setTimeout(() => {
        setIsTableAdjusting(false);
      }, 100);
    }
  };

  const handleApplyFilter = async () => {
    setIsTableAdjusting(true);
    try {
      setCurrentPage(1);
      const newFilterData = {
        status: filterData?.status,
        type: filterData?.type,
        organizationId: filterData?.organizationId,
        searchValue: searchValue,
      };
      setFilterDataApplied(newFilterData);

      // Call API with updated filter data
      await getIngredientListData(
        searchValue,
        1,
        newFilterData,
        rowsPerPage,
        sortOrder
      );

      if (isMobile) {
        setOpenFilterDrawer(false);
      }
    } finally {
      setTimeout(() => {
        setIsTableAdjusting(false);
      }, 100);
    }
  };

  const handleClearFilter = async () => {
    await handleClearSearch();
    if (isMobile) {
      setOpenFilterDrawer(false);
    }
  };

  const handleKeyPress = async (event) => {
    if (event?.key === 'Enter') {
      await handleSearch();
      setOpenFilterDrawer(false);
    }
  };

  const handleOpenAddEditModal = (item) => {
    setSingleData(item);
    setAddEditModal(true);
  };

  const handleCloseAddEditModal = async (shouldRefresh = false) => {
    setAddEditModal(false);
    setSingleData('');

    // Refresh the list if operation was successful
    if (shouldRefresh) {
      await getIngredientListData(
        filterDataApplied.searchValue || '',
        currentPage,
        filterDataApplied,
        rowsPerPage,
        sortOrder
      );
    }
  };

  // Pagination handlers
  const handlePageChange = async (newPage) => {
    setCurrentPage(newPage);
    await getIngredientListData(
      filterDataApplied.searchValue || '',
      newPage,
      filterDataApplied,
      rowsPerPage,
      sortOrder
    );
  };

  const handleRowsPerPageChange = async (newRowsPerPage) => {
    setRowsPerPage(newRowsPerPage);
    setCurrentPage(1);
    await getIngredientListData(
      filterDataApplied.searchValue || '',
      1,
      filterDataApplied,
      newRowsPerPage,
      sortOrder
    );
  };

  const columns = createColumns(
    sortOrder,
    handleSort,
    handleOpenAddEditModal,
    handleDelete,
    currentPage,
    rowsPerPage,
    filteredIngredients
  );

  return (
    <Box className="ingredient-list-container h100">
      <Box className="recipe-category-filter-wrap">
        <Box className="section-right-title">
          <Typography className="sub-header-text">
            Ingredient Category
          </Typography>
        </Box>
        <Box className="mr8 pr4">
          <CustomButton
            title="Add New"
            startIcon={<AddIcon />}
            onClick={() => handleOpenAddEditModal('')}
          />
        </Box>
      </Box>

      <Divider />
      <Box className="section-right-content recipe-category-filters">
        <Box className="search-section-wrap">
          {!isMobile &&
            selectedFilters?.map((key) => {
              const filter = filters?.find((f) => f?.key === key);
              return filter?.permission ? (
                <React.Fragment key={key}>
                  {key === 'search' ? (
                    <Box className="search-section-fields">
                      <CustomSearch
                        fullWidth
                        setSearchValue={setSearchValue}
                        onKeyPress={handleKeyPress}
                        searchValue={searchValue}
                      />
                    </Box>
                  ) : (
                    <Box className="search-section-fields">
                      <CustomSelect
                        placeholder={filter?.label}
                        options={filter?.options}
                        value={
                          filter?.options?.find((opt) => {
                            return opt?.value === filterData[key];
                          }) || ''
                        }
                        onChange={(e) =>
                          setFilterData({
                            ...filterData,
                            [key]: e?.value,
                          })
                        }
                        menuPortalTarget={document.body}
                        styles={{
                          menuPortal: (base) => ({
                            ...base,
                            zIndex: 9999,
                          }),
                        }}
                      />
                    </Box>
                  )}
                </React.Fragment>
              ) : null;
            })}

          {!isMobile && (
            <>
              <Box>
                <CustomButton
                  isIconOnly
                  startIcon={
                    <Tooltip
                      title={
                        <Typography className="sub-title-text">
                          Apply Filter
                        </Typography>
                      }
                      arrow
                      classes={{
                        tooltip: 'info-tooltip-container',
                      }}
                    >
                      <CheckIcon />
                    </Tooltip>
                  }
                  onClick={handleApplyFilter}
                />
              </Box>
              <Box>
                <CustomButton
                  variant="outlined"
                  isIconOnly
                  startIcon={
                    <Tooltip
                      title={
                        <Typography className="sub-title-text">
                          Clear Filter
                        </Typography>
                      }
                      arrow
                      classes={{
                        tooltip: 'info-tooltip-container',
                      }}
                    >
                      <ClearOutlinedIcon />
                    </Tooltip>
                  }
                  onClick={handleClearFilter}
                />
              </Box>
            </>
          )}
          <Box>
            <CustomButton
              isIconOnly
              startIcon={
                <Tooltip
                  title={
                    <Typography className="sub-title-text">Filter</Typography>
                  }
                  classes={{
                    tooltip: 'info-tooltip-container',
                  }}
                  arrow
                >
                  <FilterListIcon />
                </Tooltip>
              }
              onClick={() => {
                setOpenFilterDrawer(true);
              }}
            />
          </Box>
        </Box>
        <Box
          className="table-container table-layout"
          sx={{
            // Disable transitions on container
            transition: 'none !important',
            '& *': {
              transition: 'none !important',
              animation: 'none !important',
            },
          }}
        >
          {isTableAdjusting ? (
            <ContentLoader />
          ) : filteredIngredients.length === 0 ? (
            <NoDataView
              image={<CommonNoDataImage />}
              title="No ingredients found"
              description="There is no ingredients available at the moment."
              className="no-data-auto-margin-height-conainer"
            />
          ) : (
            <>
              <DataGrid
                key={`datagrid-${filteredIngredients.length}-${currentPage}`}
                rows={filteredIngredients}
                columns={columns}
                pageSize={rowsPerPage}
                rowCount={totalCount}
                checkboxSelection={false}
                disableSelectionOnClick
                hideMenuIcon
                paginationMode="server"
                disableVirtualization={false}
                getRowHeight={() => 'auto'}
                sx={{
                  transition: 'none !important',
                  animation: 'none !important',
                  '& *': {
                    transition: 'none !important',
                    animation: 'none !important',
                    transform: 'none !important',
                  },
                  [`& .${gridClasses.cell}`]: {
                    transition: 'none',
                  },
                }}
              />
              <CustomOrgPagination
                currentPage={currentPage}
                totalCount={totalCount}
                rowsPerPage={rowsPerPage}
                onPageChange={handlePageChange}
                OnRowPerPage={handleRowsPerPageChange}
              />
            </>
          )}
        </Box>
        <RightDrawer
          anchor={'right'}
          open={openFilterDrawer}
          onClose={() => setOpenFilterDrawer(false)}
          title="Filter"
          className="filter-options-drawer"
          content={
            <FilterComponent
              filters={filters}
              filterData={filterData}
              setFilterData={setFilterData}
              selectedFilters={selectedFilters}
              toggleFilter={toggleFilter}
              saveLayout={saveLayout}
              setOpenFilterDrawer={setOpenFilterDrawer}
              setSelectedFilters={setSelectedFilters}
              getFirstFourFilters={getFirstFourFilters}
              setSearchValue={setSearchValue}
              searchValue={searchValue}
              handleKeyPress={handleKeyPress}
              isMobile={isMobile}
              handleApplyFilter={handleApplyFilter}
              handleClearFilter={handleClearFilter}
            />
          }
        />
        <DialogBox
          open={deleteDialogOpen}
          handleClose={handleCloseDeleteDialog}
          title="Confirmation"
          className="delete-modal"
          dividerClass="delete-modal-divider"
          content={
            <DeleteModal
              handleCancel={handleCloseDeleteDialog}
              handleConfirm={handleConfirmDelete}
              text="Are you sure you want to delete this? This action cannot be undone."
            />
          }
        />
        <DialogBox
          open={addEditModal}
          handleClose={handleCloseAddEditModal}
          title={
            singleData
              ? 'Update Ingredient Category'
              : 'Add Ingredient Category'
          }
          className="small-dialog-box-container"
          content={
            <AddEditIngredientCat
              singleData={singleData}
              handleCloseAddEditModal={handleCloseAddEditModal}
            />
          }
        />
      </Box>
    </Box>
  );
}
