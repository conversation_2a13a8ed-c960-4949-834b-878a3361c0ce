'use client';
import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Tooltip,
  gridClasses,
  useTheme,
  useMediaQuery,
} from '@mui/material';
import { ArrowUpward, ArrowDownward } from '@mui/icons-material';
import { DataGrid } from '@mui/x-data-grid';
import CustomSearch from '@/components/UI/CustomSearch';
import CustomButton from '@/components/UI/CustomButton';
import ContentLoader from '@/components/UI/ContentLoader';
import NoDataView from '@/components/UI/NoDataView';
import CustomSelect from '@/components/UI/CustomSelect';
import ClearOutlinedIcon from '@mui/icons-material/ClearOutlined';
import CheckIcon from '@mui/icons-material/Check';
import CustomOrgPagination from '@/components/UI/customPagination';
import FilterListIcon from '@mui/icons-material/FilterList';
import RightDrawer from '@/components/UI/RightDrawer';
import FilterComponent from '@/components/UI/FilterComponent';
import { getPublicCTAAnalytics } from '@/services/recipeService';
import { DateFormat, setApiMessage } from '@/helper/common/commonFunctions';
import { fetchFromStorage, saveToStorage } from '@/helper/context';
import { identifiers } from '@/helper/constants/identifier';
import { staticOptions } from '@/helper/common/staticOptions';
import CommonNoDataImage from '@/components/Recipes/Recipes/CommonNoDataImage';

const createSortableHeader = (field, label, sortOrder, onSort) => (
  <Box className="d-flex align-center gap-5">
    <Box className="wrap-header-text d-flex align-center">
      <Typography className="title-text fw600">{label}</Typography>
      <Box className="amount-text arrow-wrap">
        {sortOrder?.key === field && sortOrder?.value === 'DESC' ? (
          <ArrowDownward
            className="arrow-icon cursor-pointer"
            fontSize="small"
            onClick={() => onSort(field)}
          />
        ) : (
          <ArrowUpward
            className="arrow-icon cursor-pointer"
            fontSize="small"
            onClick={() => onSort(field)}
          />
        )}
      </Box>
    </Box>
  </Box>
);

// CTA Analytics Columns
const createCTAColumns = (
  sortOrder,
  handleSort,
  currentPage,
  rowsPerPage,
  paginatedData
) => [
  {
    field: 'id',
    headerName: 'ID',
    width: 48,
    minWidth: 48,
    sortable: false,
    flex: 0,
    headerAlign: 'start',
    align: 'start',
    renderCell: (params) => {
      const rowIndex = paginatedData.findIndex(
        (row) => row?.id === params?.row?.id
      );
      const sequentialNumber = (currentPage - 1) * rowsPerPage + rowIndex + 1;
      return (
        <Typography className="text-ellipsis">{sequentialNumber}</Typography>
      );
    },
  },
  {
    field: 'recipeName',
    headerName: 'Recipe Name',
    width: 150,
    minWidth: 150,
    flex: 1,
    sortable: false,
    headerAlign: 'start',
    align: 'start',
    renderHeader: () =>
      createSortableHeader(
        'recipe_title',
        'Recipe Name',
        sortOrder,
        handleSort
      ),
    renderCell: (params) => (
      <Box className="h100 d-flex align-center">
        <Box className="">
          {params?.row?.recipeName ? (
            <Tooltip
              classes={{ tooltip: 'info-tooltip-container' }}
              title={
                <Typography className="sub-title-text">
                  {params?.row?.recipeName}
                </Typography>
              }
              arrow
            >
              <Typography className="title-text">
                {params?.row?.recipeName}
              </Typography>
            </Tooltip>
          ) : (
            <Typography className="title-text">-</Typography>
          )}
        </Box>
      </Box>
    ),
  },
  {
    field: 'ctaType',
    headerName: 'CTA Type',
    width: 100,
    minWidth: 100,
    flex: 1,
    sortable: false,
    headerAlign: 'start',
    align: 'start',
    renderCell: (params) => {
      return (
        <Typography className="title-text">
          {params?.row?.ctaType ?? '-'}
        </Typography>
      );
    },
  },
  {
    field: 'clicks',
    headerName: 'Clicks',
    width: 100,
    minWidth: 100,
    flex: 1,
    sortable: false,
    headerAlign: 'center',
    align: 'center',
    renderHeader: () =>
      createSortableHeader('clicks', 'Clicks', sortOrder, handleSort),
    renderCell: (params) => (
      <Typography className="title-text">
        {params?.row?.clicks ?? '-'}
      </Typography>
    ),
  },
  {
    field: 'lastClickedAt',
    headerName: 'Last Clicked At',
    width: 100,
    minWidth: 100,
    flex: 1,
    sortable: false,
    headerAlign: 'center',
    align: 'center',
    renderHeader: () =>
      createSortableHeader(
        'created_at',
        'Last Clicked At',
        sortOrder,
        handleSort
      ),
    renderCell: (params) => (
      <Typography className="title-text">
        {params?.row?.lastClickedAt
          ? DateFormat(params?.row?.lastClickedAt, 'datesWithhour')
          : '-'}
      </Typography>
    ),
  },
];

const RecipeCTAAnalyticsTable = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down(1500));

  // CTA Analytics State (following ContactSubmissionsTable pattern)
  const [currentPage, setCurrentPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [searchValue, setSearchValue] = useState('');
  const [sortOrder, setSortOrder] = useState({
    key: '',
    value: 'ASC',
  });
  const [filterData, setFilterData] = useState({
    ctaType: '',
    dateRange: '',
  });
  const [filterDataApplied, setFilterDataApplied] = useState({
    ctaType: '',
    dateRange: '',
    searchValue: '',
  });
  const [isLoading, setIsLoading] = useState(false);

  // API Data State
  const [ctaData, setCtaData] = useState([]);
  const [totalCount, setTotalCount] = useState(0);

  // Filter drawer state
  const [openFilterDrawer, setOpenFilterDrawer] = useState(false);
  const [selectedFilters, setSelectedFilters] = useState([]);

  // Filter options (following ContactSubmissionsTable pattern)
  const filters = [
    {
      key: 'search',
      label: 'Search Recipe Name',
      options: [],
      permission: true,
    },
    {
      key: 'ctaType',
      label: 'CTA Type',
      permission: true,
      options: staticOptions?.CTA_ANALYTICS_TYPE_OPTIONS,
    },
    {
      key: 'dateRange',
      label: 'Date Range',
      permission: true,
      options: staticOptions?.ANALYTICS_DATE_RANGES,
    },
  ];

  // Filter management functions (following IngredientItems pattern)
  const toggleFilter = (key) => {
    setSelectedFilters((prevFilters) => {
      if (prevFilters?.includes(key)) {
        return prevFilters?.filter((item) => item !== key);
      } else {
        const index = filters.findIndex((filter) => filter.key === key);
        const newFilters = [...prevFilters];
        newFilters.splice(index, 0, key);
        return newFilters;
      }
    });
  };

  const getFirstFourFilters = () => {
    setSelectedFilters(selectedFilters?.slice(0, 4));
    saveToStorage(
      identifiers?.CTA_ANALYTICS_FILTER,
      selectedFilters?.slice(0, 4)
    );
  };

  const saveLayout = () => {
    saveToStorage(identifiers?.CTA_ANALYTICS_FILTER, selectedFilters);
    setOpenFilterDrawer(false);
  };

  const handleKeyPress = async (event) => {
    if (event?.key === 'Enter') {
      await handleSearch();
    }
  };

  // Initialize selected filters
  useEffect(() => {
    const savedFilters = fetchFromStorage(identifiers?.CTA_ANALYTICS_FILTER);
    if (!savedFilters) {
      setSelectedFilters(filters.slice(0, 4)?.map((filter) => filter?.key));
    } else {
      setSelectedFilters(savedFilters);
    }
  }, []);

  // Wrapper function for API calls (following ContactSubmissionsTable pattern)
  const getCTAAnalyticsData = async (
    search,
    page,
    filter,
    Rpp,
    Sort,
    showLoader = true
  ) => {
    try {
      if (showLoader) {
        setIsLoading(true);
      }

      // Create filter object with search as recipe_name and only required filters
      const apiFilter = {
        ...(search && { recipe_name: search }), // Pass search value as recipe_name
        ...(filter?.ctaType && { cta_type: filter?.ctaType }),
        ...(filter?.dateRange && { date_range: filter?.dateRange }),
      };

      const { data, totalRecords } = await getPublicCTAAnalytics(
        '', // Don't pass search as separate parameter
        page,
        apiFilter,
        Rpp,
        Sort
      );

      const transformedData = data?.map((item) => ({
        id: `${item?.recipe_id}-${item?.cta_type?.replace(/\s+/g, '_')}`,
        recipeName: item?.recipe_name,
        ctaType: item?.cta_type,
        clicks: item?.clicks,
        lastClickedAt: item?.last_clicked_at,
      }));

      setCtaData(transformedData);
      setTotalCount(totalRecords || transformedData.length);
    } catch (error) {
      setApiMessage(
        'error',
        error?.response?.data?.message || 'Failed to fetch CTA analytics data'
      );
      // Clear data on error since we're using API-only approach
      setCtaData([]);
      setTotalCount(0);
    } finally {
      if (showLoader) {
        setIsLoading(false);
      }
    }
  };

  // Handle sorting (following ContactSubmissionsTable pattern)
  const handleSort = async (key) => {
    const newOrder = sortOrder?.value === 'ASC' ? 'DESC' : 'ASC';
    const newSortOrder = { key, value: newOrder };
    setSortOrder(newSortOrder);
    setCurrentPage(1);

    // Call API with new sort order without showing loader
    await getCTAAnalyticsData(
      filterDataApplied.searchValue || '',
      1,
      filterDataApplied,
      rowsPerPage,
      newSortOrder,
      false // Don't show loader for sorting
    );
  };

  // Handle search (following ContactSubmissionsTable pattern)
  const handleSearch = async () => {
    try {
      // Call API with current search and filter values
      await getCTAAnalyticsData(
        searchValue,
        1, // Reset to first page
        filterDataApplied,
        rowsPerPage,
        sortOrder
      );
      // Apply current search value to filterDataApplied
      setFilterDataApplied((prev) => ({
        ...prev,
        searchValue: searchValue,
      }));
      setCurrentPage(1);
    } finally {
      // Handle any cleanup if needed
    }
  };

  const handleApplyFilters = async () => {
    try {
      setCurrentPage(1);
      const newFilterData = {
        ctaType: filterData?.ctaType,
        dateRange: filterData?.dateRange,
        searchValue: searchValue,
      };
      setFilterDataApplied(newFilterData);

      // Call API with updated filter data
      await getCTAAnalyticsData(
        searchValue,
        1,
        newFilterData,
        rowsPerPage,
        sortOrder
      );

      // Close the filter drawer after applying filters
      setOpenFilterDrawer(false);
    } finally {
      // Handle any cleanup if needed
    }
  };

  // Handle clear filters (following ContactSubmissionsTable pattern)
  const handleClearFilters = async () => {
    setSearchValue('');
    setFilterData({
      ctaType: '',
      dateRange: '',
    });
    setFilterDataApplied({
      ctaType: '',
      dateRange: '',
      searchValue: '',
    });
    setCurrentPage(1);

    // Call API with cleared filters
    try {
      await getCTAAnalyticsData(
        '',
        1,
        {
          ctaType: '',
          dateRange: '',
          searchValue: '',
        },
        rowsPerPage,
        sortOrder
      );

      // Close the filter drawer after clearing filters
      setOpenFilterDrawer(false);
    } finally {
      // Handle any cleanup if needed
    }
  };

  // Pagination handlers (following ContactSubmissionsTable pattern)
  const handlePageChange = async (newPage) => {
    setCurrentPage(newPage);
    await getCTAAnalyticsData(
      filterDataApplied.searchValue || '',
      newPage,
      filterDataApplied,
      rowsPerPage,
      sortOrder
    );
  };

  const handleRowsPerPageChange = async (newRowsPerPage) => {
    setRowsPerPage(newRowsPerPage);
    setCurrentPage(1);
    await getCTAAnalyticsData(
      filterDataApplied.searchValue || '',
      1,
      filterDataApplied,
      newRowsPerPage,
      sortOrder
    );
  };

  // Load initial data on component mount (following ContactSubmissionsTable pattern)
  useEffect(() => {
    getCTAAnalyticsData('', 1, filterDataApplied, rowsPerPage, sortOrder);
  }, []);

  // CTA Analytics columns
  const columns = createCTAColumns(
    sortOrder,
    handleSort,
    currentPage,
    rowsPerPage,
    ctaData
  );

  return (
    <Box className="">
      {/* <Box className="d-flex justify-space-between align-center pb16">
        <Typography className="sub-header-text fw600">
          Recipe CTA Analytics
        </Typography>
      </Box> */}

      {/* CTA Filters (following IngredientItems pattern) */}
      <Box className="search-section-wrap">
        {!isMobile &&
          selectedFilters?.map((key) => {
            const filter = filters?.find((f) => f?.key === key);
            return filter?.permission ? (
              <React.Fragment key={key}>
                {key === 'search' ? (
                  <Box className="search-section-fields">
                    <CustomSearch
                      fullWidth
                      placeholder="Search by recipe name..."
                      setSearchValue={setSearchValue}
                      onKeyPress={handleKeyPress}
                      searchValue={searchValue}
                    />
                  </Box>
                ) : (
                  <Box className="search-section-fields">
                    <CustomSelect
                      placeholder={filter?.label}
                      options={filter?.options}
                      value={
                        filter?.options?.find((opt) => {
                          return opt?.value === filterData[key];
                        }) || ''
                      }
                      onChange={(e) =>
                        setFilterData({
                          ...filterData,
                          [key]: e?.value,
                        })
                      }
                      menuPortalTarget={document.body}
                      styles={{
                        menuPortal: (base) => ({
                          ...base,
                          zIndex: 9999,
                        }),
                      }}
                    />
                  </Box>
                )}
              </React.Fragment>
            ) : null;
          })}

        {!isMobile && (
          <>
            <Box>
              <CustomButton
                isIconOnly
                startIcon={
                  <Tooltip
                    title={
                      <Typography className="sub-title-text">
                        Apply Filter
                      </Typography>
                    }
                    arrow
                    classes={{
                      tooltip: 'info-tooltip-container',
                    }}
                  >
                    <CheckIcon />
                  </Tooltip>
                }
                onClick={handleApplyFilters}
              />
            </Box>
            <Box>
              <CustomButton
                variant="outlined"
                isIconOnly
                startIcon={
                  <Tooltip
                    title={
                      <Typography className="sub-title-text">
                        Clear Filter
                      </Typography>
                    }
                    arrow
                    classes={{
                      tooltip: 'info-tooltip-container',
                    }}
                  >
                    <ClearOutlinedIcon />
                  </Tooltip>
                }
                onClick={handleClearFilters}
              />
            </Box>
          </>
        )}
        <Box>
          <CustomButton
            isIconOnly
            startIcon={
              <Tooltip
                title={
                  <Typography className="sub-title-text">Filter</Typography>
                }
                classes={{
                  tooltip: 'info-tooltip-container',
                }}
                arrow
              >
                <FilterListIcon />
              </Tooltip>
            }
            onClick={() => {
              setOpenFilterDrawer(true);
            }}
          />
        </Box>
      </Box>

      {/* CTA Table */}
      <Box className="table-container table-layout">
        {isLoading ? (
          <ContentLoader />
        ) : ctaData.length === 0 ? (
          <NoDataView
            image={<CommonNoDataImage />}
            title="No CTA data found"
            description="There are no CTA analytics available at the moment."
            className="no-data-auto-height-conainer"
          />
        ) : (
          <>
            <DataGrid
              key={`datagrid-cta-${ctaData.length}-${currentPage}`}
              rows={ctaData}
              columns={columns}
              pageSize={rowsPerPage}
              rowCount={totalCount}
              checkboxSelection={false}
              disableSelectionOnClick
              hideMenuIcon
              paginationMode="server"
              disableVirtualization={false}
              sx={{
                transition: 'none !important',
                animation: 'none !important',
                '& *': {
                  transition: 'none !important',
                  animation: 'none !important',
                  transform: 'none !important',
                },
                [`& .${gridClasses.cell}`]: {
                  py: 1,
                  transition: 'none',
                },
              }}
            />
            <CustomOrgPagination
              currentPage={currentPage}
              totalCount={totalCount}
              rowsPerPage={rowsPerPage}
              onPageChange={handlePageChange}
              OnRowPerPage={handleRowsPerPageChange}
            />
          </>
        )}
      </Box>

      {/* Filter Drawer (following IngredientItems pattern) */}
      <RightDrawer
        anchor={'right'}
        open={openFilterDrawer}
        onClose={() => setOpenFilterDrawer(false)}
        title="Filter"
        className="filter-options-drawer"
        content={
          <FilterComponent
            filters={filters}
            filterData={filterData}
            setFilterData={setFilterData}
            selectedFilters={selectedFilters}
            toggleFilter={toggleFilter}
            saveLayout={saveLayout}
            setOpenFilterDrawer={setOpenFilterDrawer}
            setSelectedFilters={setSelectedFilters}
            getFirstFourFilters={getFirstFourFilters}
            setSearchValue={setSearchValue}
            searchValue={searchValue}
            handleKeyPress={handleKeyPress}
            isMobile={isMobile}
            handleApplyFilter={handleApplyFilters}
            handleClearFilter={handleClearFilters}
          />
        }
      />
    </Box>
  );
};

export default RecipeCTAAnalyticsTable;
