'use client';

import React, { useState, useEffect } from 'react';
import { Box, Typography, Tooltip } from '@mui/material';
import { DataGrid, gridClasses } from '@mui/x-data-grid';
import ContentLoader from '@/components/UI/ContentLoader';
import NoDataView from '@/components/UI/NoDataView';
import { DateFormat, setApiMessage } from '@/helper/common/commonFunctions';
import CustomOrgPagination from '@/components/UI/customPagination';
import ResetIcon from '@/components/ActionIcons/ResetIcon';
import BranchDepartmentDisplay from '@/components/BranchDepartmentDisplay';
import CommonUserDetails from '@/components/UI/CommonUserDetails';
import {
  getRecipeViewStatistics,
  resetRecipeViewStatistics,
} from '@/services/recipeService';
import CommonNoDataImage from '@/components/Recipes/Recipes/CommonNoDataImage';
import './recipestafftracking.scss';

const RecipeStaffTrackingTable = React.forwardRef(({ recipeId }, ref) => {
  const [trackingData, setTrackingData] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalCount, setTotalCount] = useState(0);

  // Fetch staff tracking data
  const fetchStaffTrackingData = async () => {
    try {
      setIsLoading(true);

      const response = await getRecipeViewStatistics(recipeId);

      if (response?.data) {
        setTrackingData(response?.data || []);
        setTotalCount(response?.data?.length || 0);
      } else {
        setTrackingData([]);
        setTotalCount(0);
      }
    } catch (error) {
      console.error('Error fetching staff tracking data:', error);
      setApiMessage('error', error?.message);
      setTrackingData([]);
      setTotalCount(0);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle pagination
  const handlePageChange = (newPage) => {
    setCurrentPage(newPage);
  };

  const handleRowsPerPageChange = (newRowsPerPage) => {
    setRowsPerPage(newRowsPerPage);
    setCurrentPage(1);
  };

  // Handle individual reset directly (no confirmation)
  const handleResetClick = async (userId) => {
    try {
      const data = await resetRecipeViewStatistics(recipeId, [userId]);
      // Refresh data after reset
      await fetchStaffTrackingData();
      setApiMessage('success', data?.message);
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  // Expose fetchStaffTrackingData to parent component through ref
  React.useImperativeHandle(ref, () => ({
    refreshData: fetchStaffTrackingData,
  }));

  // Table columns
  const columns = [
    {
      field: 'id',
      headerName: 'ID',
      width: 48,
      minWidth: 48,
      sortable: false,
      flex: 0,
      headerAlign: 'start',
      align: 'start',
      renderHeader: () => (
        <Box className="d-flex align-center justify-start">
          <Typography className="title-text cursor-pointer fw600">
            ID
          </Typography>
        </Box>
      ),
      renderCell: (params) => {
        // Find the index of this row in the data
        const rowIndex =
          trackingData?.findIndex((row) => row?.id === params?.row?.id) || 0;
        const sequentialNumber = rowIndex + 1;
        return (
          <Box className="d-flex align-center justify-start h100">
            <Typography className="text-ellipsis">
              {sequentialNumber}
            </Typography>
          </Box>
        );
      },
    },
    {
      field: 'user_full_name',
      headerName: 'Staff Name',
      width: 260,
      minWidth: 260,
      flex: 1,
      sortable: false,
      headerAlign: 'start',
      align: 'start',
      renderCell: (params) => (
        <CommonUserDetails
          userData={params?.row}
          page={currentPage}
          rowsPerPage={rowsPerPage}
        />
      ),
    },
    {
      field: 'user_branch',
      headerName: 'Branch / Dep.',
      width: 200,
      minWidth: 200,
      flex: 1,
      sortable: false,
      headerAlign: 'start',
      align: 'start',
      renderCell: (params) => {
        // Format data for BranchDepartmentDisplay component
        const formattedRow = {
          ...params?.row,
          branch: {
            branch_name: params?.row?.user_branch || '',
          },
          department: {
            department_name: params?.row?.user_department || '',
          },
        };
        return <BranchDepartmentDisplay row={formattedRow} />;
      },
    },
    {
      field: 'last_recipe_view',
      headerName: 'Last View',
      width: 200,
      minWidth: 200,
      sortable: false,
      flex: 1,
      headerAlign: 'center',
      align: 'center',
      renderCell: (params) => (
        <Box className="d-flex align-center justify-center h100 gap-sm">
          <Typography className="title-text text-ellipsis">
            {params?.value ? DateFormat(params?.value, 'datesWithhour') : '-'}
          </Typography>
        </Box>
      ),
    },
    {
      field: 'total_view_count',
      headerName: 'Recipe Views',
      width: 100,
      minWidth: 100,
      flex: 1,
      sortable: false,
      headerAlign: 'center',
      align: 'center',
      renderCell: (params) => (
        <Box className="gap-5 h100 d-flex align-center justify-center">
          <Typography className="title-text">
            {params?.row?.total_view_count || 0}
          </Typography>
        </Box>
      ),
    },
    {
      field: 'actions',
      headerName: 'Actions',
      width: 120,
      minWidth: 120,
      flex: 1,
      sortable: false,
      headerAlign: 'center',
      align: 'center',
      renderCell: (params) => {
        const isDisabled = (params?.row?.total_view_count || 0) === 0;
        return (
          <Box className="d-flex actions align-center justify-center h100">
            {isDisabled ? (
              <Box className="reset-button-container reset-button-container--disabled">
                <ResetIcon className="reset-icon" />
              </Box>
            ) : (
              <Tooltip
                title={
                  <Typography className="sub-title-text">Reset Data</Typography>
                }
                arrow
                classes={{
                  tooltip: 'info-tooltip-container',
                }}
              >
                <Box className="reset-button-container reset-button-container--enabled">
                  <ResetIcon
                    className="reset-icon"
                    onClick={() => handleResetClick(params?.row?.id)}
                  />
                </Box>
              </Tooltip>
            )}
          </Box>
        );
      },
    },
  ];

  useEffect(() => {
    if (recipeId) {
      fetchStaffTrackingData();
    }
  }, [recipeId]);

  return (
    <div className="recipe-staff-tracking-table">
      {/* Table */}
      <Box className="table-container table-layout">
        {isLoading ? (
          <ContentLoader />
        ) : trackingData?.length === 0 ? (
          <NoDataView
            image={<CommonNoDataImage />}
            title="No Staff Tracking Data"
            description="There is no staff tracking data available for this recipe."
            className="no-data-auto-margin-height-conainer"
          />
        ) : (
          <>
            <DataGrid
              rows={trackingData || []}
              columns={columns}
              pageSize={rowsPerPage}
              checkboxSelection={false}
              disableSelectionOnClick
              hideMenuIcon
              disableVirtualization={false}
              getRowHeight={() => 'auto'}
              sx={{
                [`& .${gridClasses.cell}`]: {
                  py: 1,
                },
                transition: 'none !important',
                animation: 'none !important',
                '& *': {
                  transition: 'none !important',
                  animation: 'none !important',
                  transform: 'none !important',
                },
              }}
            />
            {trackingData?.length > rowsPerPage && (
              <CustomOrgPagination
                currentPage={currentPage}
                totalCount={totalCount}
                rowsPerPage={rowsPerPage}
                onPageChange={handlePageChange}
                OnRowPerPage={handleRowsPerPageChange}
              />
            )}
          </>
        )}
      </Box>
    </div>
  );
});

RecipeStaffTrackingTable.displayName = 'RecipeStaffTrackingTable';

export default RecipeStaffTrackingTable;
