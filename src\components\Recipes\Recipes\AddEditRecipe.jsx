'use client';

import React, { useContext, useEffect, useReducer, useState } from 'react';
import { Box, Tooltip, Typography } from '@mui/material';
import { useRouter, usePathname } from 'next/navigation';
import Icon from '@/components/UI/AppIcon/AppIcon';
import CustomButton from '@/components/UI/CustomButton';
import TopBackPageButton from '@/components/TopBackPage/TopBackPage';
import FormProgressIndicator from './components/FormProgressIndicator/FormProgressIndicator';
import BasicInfoSection from './components/BasicInfoSection/BasicInfoSection';
import MediaUploadSection from './components/MediaUploadSection/MediaUploadSection';
import IngredientsSection from './components/IngredientsSection/IngredientsSection';
import InstructionsSection from './components/InstructionsSection/InstructionsSection';
import NutritionSection from './components/NutritionSection/NutritionSection';
import ServingDetailsSection from './components/ServingDetailsSection/ServingDetailsSection';
import SectionNavigationSidebar from './components/SectionNavigationSidebar/SectionNavigationSidebar';
import MainFormContent from './components/MainFormContent/MainFormContent';
import HACCPSection from './components/HACCPSection/HACCPSection';
import DialogBox from '@/components/UI/Modalbox';
import ConfirmationModal from '@/components/UI/ConfirmationModal';
import DeleteModal from '@/components/UI/DeleteModal';
import AuthContext from '@/helper/authcontext';
import {
  getCurrencySymbol,
  isPublicRoute,
  setApiMessage,
} from '@/helper/common/commonFunctions';
import {
  // createRecipe,
  // updateRecipe,
  getRecipePreviewData,
  createUpdateRecipe,
} from '@/services/recipeService';
import PreLoader from '@/components/UI/Loader';
import { RECIPE_URLS } from '@/helper/constants/urls';
import {
  fetchFromStorage,
  removeFromStorage,
  saveToStorage,
} from '@/helper/context';
import { identifiers } from '@/helper/constants/identifier';
import './recipes.scss';

// Recipe form reducer
const recipeFormReducer = (state, action) => {
  switch (action.type) {
    case 'SET_FORM_DATA':
      return { ...state, ...action.payload };
    case 'UPDATE_BASIC_INFO':
      return { ...state, basicInfo: { ...state.basicInfo, ...action.payload } };
    case 'UPDATE_MEDIA':
      return { ...state, media: { ...state.media, ...action.payload } };
    case 'UPDATE_INGREDIENTS':
      return { ...state, ingredients: action.payload };
    case 'UPDATE_INSTRUCTIONS':
      return { ...state, instructions: action.payload };
    case 'UPDATE_NUTRITION':
      return { ...state, nutrition: { ...state.nutrition, ...action.payload } };
    case 'UPDATE_SERVING':
      return { ...state, serving: { ...state.serving, ...action.payload } };
    case 'UPDATE_HACCP':
      return { ...state, haccp: { ...state.haccp, ...action.payload } };
    case 'SET_ACTIVE_SECTION':
      return { ...state, activeSection: action.payload };
    case 'CALCULATE_TOTALS':
      const totalCost = state.ingredients.reduce((sum, ing) => {
        const cost = ing?.cost || 0;
        const quantity = ing?.quantity || 0;
        const wasteMultiplier = 1 + (ing?.ingredient_wastage || 0) / 100;
        const finalCost = cost * quantity * wasteMultiplier;
        return sum + finalCost;
      }, 0);
      const totalTime =
        (state.basicInfo.prepTime || 0) + (state.basicInfo.cookTime || 0);
      const portionCost =
        state.serving.totalPortions > 0
          ? totalCost / state.serving.totalPortions
          : 0;
      return {
        ...state,
        calculations: {
          totalCost,
          totalTime,
          portionCost,
        },
      };
    default:
      return state;
  }
};

const initialState = {
  activeSection: 'basic-info',
  basicInfo: {
    recipeName: '',
    publicDisplayName: '',
    recipeDescription: '',
    categories: [],
    dietaries: [],
    prepTime: 0,
    cookTime: 0,
    visibility: ['private'],
    complexity_level: 'low',
  },
  media: {
    mainImage: null,
    additionalImages: [],
    documents: [],
    audio: [],
    links: [],
  },
  isCookingMethod: false,
  isPreparationMethod: false,
  ingredients: [],
  instructions: [],
  nutrition: {
    nutrition: [],
    commonAllergens: [],
    mayContainAllergens: [],
  },
  serving: {
    yield: { value: 0, unit: 'servings' },
    totalPortions: 0,
    singlePortionSize: 0,
    servingMethod: '',
    serveIn: '',
    garnish: '',
    fohTips: '',
    chefTips: '',
  },
  haccp: {
    useFrom: null,
    sections: [],
  },
  calculations: {
    totalCost: 0,
    totalTime: 0,
    portionCost: 0,
  },
  recipeStatus: '',
  lastUpdated: '',
  recipeId: null,
  is_cost_manual: false,
};

export default function AddEditRecipe({ isUpdate, slug }) {
  const router = useRouter();
  const pathname = usePathname();
  const { authState } = useContext(AuthContext);
  const [formState, dispatch] = useReducer(recipeFormReducer, initialState);
  // const [initialFormState, setInitialFormState] = useState(null);
  const [isMobileView, setIsMobileView] = useState(false);
  const [showExitModal, setShowExitModal] = useState(false);
  const [validationErrors, setValidationErrors] = useState({});
  const [isSaving, setIsSaving] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const currency = getCurrencySymbol(authState?.currency_details);

  const [oldRecipeDatas, setOldRecipeDatas] = useState(null);
  const [backButtonModal, setBackButtonModal] = useState(false);

  const updatedData = fetchFromStorage(identifiers.RECIPE_CREATION_DATA);

  const handleRedirect = () => {
    if (hasUnsavedChanges()) {
      setBackButtonModal(true);
    } else {
      router.back();
    }
  };

  // Load recipe data for update mode
  const loadRecipeData = async (slugId) => {
    try {
      setIsLoading(true);
      const response = await getRecipePreviewData(slugId, pathname);
      const { singleRecipe: recipeData, organization_details } = response;
      const isPublic = isPublicRoute(pathname);
      if (isPublic) {
        saveToStorage(
          identifiers.RECIPE_PUBLIC_ORG_DATA,
          organization_details || ''
        );
      }
      const transformedData = {
        activeSection: 'basic-info',
        basicInfo: {
          recipeName: recipeData?.recipe_title || '',
          publicDisplayName: recipeData?.recipe_public_title || '',
          recipeDescription: recipeData?.recipe_description || '',
          categories: recipeData?.categories?.map((cat) => cat?.id) || [],
          dietaries:
            recipeData?.dietary_attributes?.map((diet) => diet?.id) || [],
          prepTime: recipeData?.recipe_preparation_time / 60 || 0,
          cookTime: recipeData?.recipe_cook_time / 60 || 0,
          complexity_level: recipeData?.recipe_complexity_level || 'low',
          visibility: [
            ...(recipeData?.has_recipe_public_visibility ? ['public'] : []),
            ...(recipeData?.has_recipe_private_visibility ? ['private'] : []),
          ],
        },
        media: {
          mainImage:
            (recipeData?.item_detail?.item_link && {
              url: recipeData?.item_detail?.item_link || '',
              id: recipeData?.item_detail?.item_id || '',
              name: recipeData?.recipe_title || '',
            }) ||
            null,
          additionalImages: [
            ...(recipeData?.recipeFiles || []),
            ...(recipeData?.resources
              ?.filter(
                (res) =>
                  res?.type === 'item' &&
                  (res?.item_detail?.item_type?.includes('image') ||
                    res?.item_detail?.item_type?.includes('video'))
              )
              ?.map((item) => ({
                url: item?.item_detail?.item_link,
                id: item?.item_detail?.item_id,
                type: item?.item_detail?.item_type,
                name: item?.item_detail?.item_link?.split('/').pop() || '',
              })) || []),
          ],
          documents:
            recipeData?.resources
              ?.filter(
                (res) =>
                  res?.type === 'item' &&
                  (res?.item_detail?.item_type?.includes('pdf') ||
                    res?.item_detail?.item_type?.includes('document'))
              )
              ?.map((doc) => ({
                url: doc?.item_detail?.item_link,
                id: doc?.item_detail?.item_id,
                type: doc?.item_detail?.item_type,
                name: doc?.item_detail?.item_link?.split('/').pop() || '',
              })) || [],
          audio:
            recipeData?.resources
              ?.filter(
                (res) =>
                  res?.type === 'item' &&
                  res?.item_detail?.item_type?.includes('audio')
              )
              ?.map((audio) => ({
                url: audio?.item_detail?.item_link,
                id: audio?.item_detail?.item_id,
                type: audio?.item_detail?.item_type,
                name: audio?.item_detail?.item_link?.split('/').pop() || '',
              })) || [],
          links:
            recipeData?.resources
              ?.filter((res) => res?.type === 'link')
              ?.map((link) => ({
                ...link,
                item_link_type: link?.item_detail?.item_type,
                url: link?.item_detail?.item_link,
                id: link?.item_id,
              })) || [],
        },

        ingredients:
          recipeData?.ingredients?.map((ing) => {
            return {
              ...ing,
              quantity: ing?.ingredient_quantity || 0,
              cost: ing?.ingredient_cost || 0,
              measure_of_cost: {
                id: ing?.measure_id,
                unit_title: ing?.measure_slug,
              },
              preparationMethod: ing?.preparation_method || null,
              cookingMethod: ing?.ingredient_cooking_method || null,
              allergy: ing?.allergen_attributes || [],
              updatedPerUnitCost: ing?.ingredient_cost || 0,
              nutrition:
                ing?.nutrition_attributes?.map((nut) => {
                  return {
                    ...nut,
                    value: nut?.unit,
                  };
                }) || [],
            };
          }) || [],
        instructions:
          recipeData?.steps?.map((step) => {
            return {
              ...step,
              stepNumber: step?.recipe_step_order || 0,
              description: step?.recipe_step_description || '',
              image: {
                url: step?.item_detail?.item_link || null,
                id: step?.item_id,
              },
            };
          }) || [],
        nutrition: {
          nutrition:
            recipeData?.nutrition_attributes?.map((nut) => {
              return {
                ...nut,
                value: nut?.unit,
              };
            }) || [],
          commonAllergens:
            recipeData?.allergen_attributes?.contains?.map(
              (allergen) => allergen?.id
            ) || [],
          mayContainAllergens:
            recipeData?.allergen_attributes?.may_contain?.map(
              (allergen) => allergen?.id
            ) || [],
        },
        serving: {
          yield: {
            value: recipeData?.recipe_yield || 0,
            unit: recipeData?.recipe_yield_unit || '',
          },
          totalPortions: recipeData?.recipe_total_portions || 0,
          singlePortionSize: recipeData?.recipe_single_portion_size || 0,
          servingMethod: recipeData?.recipe_serving_method || '',
          serveIn: recipeData?.recipe_serve_in || '',
          garnish: recipeData?.recipe_garnish || '',
          fohTips: recipeData?.recipe_foh_tips || '',
          chefTips: recipeData?.recipe_head_chef_tips || '',
        },
        haccp: {
          useFrom: recipeData?.haccp_use_from,
          sections:
            recipeData?.haccp_attributes?.map((section) => ({
              id: section?.id,
              haccp_id: section?.id,
              title: section?.attribute_title || '',
              description: section?.attribute_description || '',
              useDefault: section?.use_default || false,
            })) || [],
        },
        isCookingMethod: !!recipeData?.is_cooking_method || false,
        isPreparationMethod: !!recipeData?.is_preparation_method || false,
        recipeStatus: recipeData?.recipe_status || '',
        lastUpdated: recipeData?.updated_at || '',
        recipeId: recipeData?.id || null,
        recipe_slug: recipeData?.recipe_slug || null,
        is_cost_manual: recipeData?.is_cost_manual || false,
      };

      const updatedResponse = {
        recipe_id: recipeData?.id,
        recipe_title: recipeData?.recipe_title,
        recipe_placeholder: recipeData?.recipe_placeholder,
        ingredients: recipeData?.ingredients,
        steps: recipeData?.steps,
        nutrition_attributes: recipeData?.nutrition_attributes,
        allergen_attributes: {
          contains: recipeData?.allergen_attributes?.contains,
          may_contain: recipeData?.allergen_attributes?.may_contain,
        },
        recipe_yield: recipeData?.recipe_yield,
        recipe_total_portions: recipeData?.recipe_total_portions,
        haccp_attributes: recipeData?.haccp_attributes,
      };

      // for handle side menu
      saveToStorage(identifiers.RECIPE_CREATION_DATA, updatedResponse);

      // Set both initial and current form state
      // setInitialFormState(transformedData);
      setOldRecipeDatas(transformedData);
      dispatch({ type: 'SET_FORM_DATA', payload: transformedData });
    } catch (error) {
      setApiMessage('error', error?.message || 'Failed to load recipe data');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    removeFromStorage(identifiers.RECIPE_PUBLIC_ORG_DATA);
    if (isUpdate && slug) {
      loadRecipeData(slug);
    } else if (updatedData && updatedData?.recipe_id) {
      loadRecipeData(updatedData?.recipe_id);
    }
  }, [isUpdate, slug, pathname]);

  // Set initialFormState for create mode
  useEffect(() => {
    setOldRecipeDatas(initialState);
  }, []);

  // Check for mobile view
  useEffect(() => {
    const checkMobileView = () => {
      setIsMobileView(window.innerWidth < 768);
    };

    checkMobileView();
    window.addEventListener('resize', checkMobileView);
    return () => window.removeEventListener('resize', checkMobileView);
  }, []);

  // Auto-calculate totals when dependencies change
  useEffect(() => {
    dispatch({ type: 'CALCULATE_TOTALS' });
  }, [
    formState.ingredients,
    formState.basicInfo.prepTime,
    formState.basicInfo.cookTime,
    formState.serving.totalPortions,
  ]);

  // Intercept browser/app back and route changes
  useEffect(() => {
    // Intercept browser back (popstate) and tab close/refresh (beforeunload)
    const handleNavigationAttempt = () => {
      if (hasUnsavedChanges()) {
        setBackButtonModal(true);
        window.history.pushState(null, '', window.location.href); // prevent navigation
      }
    };

    const handlePopState = () => handleNavigationAttempt();
    const handleBeforeUnload = (e) => {
      if (hasUnsavedChanges()) {
        e.preventDefault();
        // setBackButtonModal(true);
        window.history.pushState(null, '', window.location.href); // prevent navigation
        return '';
      }
    };

    window.addEventListener('popstate', handlePopState);
    window.addEventListener('beforeunload', handleBeforeUnload);

    return () => {
      window.removeEventListener('popstate', handlePopState);
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [formState]);

  const handleOpenBackModal = async () => {
    if (checkSingleRequiredValidate()) {
      handleSaveAndExit('draft');
      setBackButtonModal(false);
    }
  };

  // Function to extract section data for comparison
  const getSectionData = (state, section, updatedData) => {
    const recipeData = prepareRecipeData(state);
    switch (section) {
      case 'basic-info':
        return {
          ...(updatedData?.recipe_id && { recipe_id: updatedData?.recipe_id }),
          recipe_title: recipeData?.recipe_title,
          recipe_public_title: recipeData?.recipe_public_title,
          recipe_description: recipeData?.recipe_description,
          recipe_preparation_time: recipeData?.recipe_preparation_time,
          recipe_cook_time: recipeData?.recipe_cook_time,
          has_recipe_public_visibility:
            recipeData?.has_recipe_public_visibility,
          has_recipe_private_visibility:
            recipeData?.has_recipe_private_visibility,
          categories: recipeData?.categories,
          recipe_complexity_level: recipeData?.recipe_complexity_level,
          dietary_attributes: recipeData?.dietary_attributes,
        };
      case 'media':
        return {
          recipe_id: updatedData?.recipe_id,
          mainImage: state?.media?.mainImage,
          additionalImages: state?.media?.additionalImages,
          documents: state?.media?.documents,
          audio: state?.media?.audio,
          links: state?.media?.links,
        };
      case 'ingredients':
        return {
          recipe_id: updatedData?.recipe_id,
          ingredients: recipeData?.ingredients,
          is_ingredient_cooking_method: recipeData?.is_cooking_method,
          is_preparation_method: recipeData?.is_preparation_method,
        };
      case 'nutrition':
        return {
          recipe_id: updatedData?.recipe_id,
          nutrition_attributes: recipeData?.nutrition_attributes,
          allergen_attributes: {
            contains: recipeData?.allergen_attributes?.contains,
            may_contain: recipeData?.allergen_attributes?.may_contain,
          },
        };
      case 'instructions':
        return {
          recipe_id: updatedData?.recipe_id,
          steps: recipeData?.steps,
        };
      case 'serving':
        return {
          recipe_id: updatedData?.recipe_id,
          recipe_yield: recipeData?.recipe_yield,
          recipe_yield_unit: recipeData?.recipe_yield_unit,
          recipe_total_portions: recipeData?.recipe_total_portions,
          recipe_single_portion_size: recipeData?.recipe_single_portion_size,
          recipe_serving_method: recipeData?.recipe_serving_method,
          recipe_serve_in: recipeData?.recipe_serve_in,
          recipe_garnish: recipeData?.recipe_garnish,
          recipe_foh_tips: recipeData?.recipe_foh_tips,
          recipe_head_chef_tips: recipeData?.recipe_head_chef_tips,
        };
      case 'haccp':
        return {
          recipe_id: updatedData?.recipe_id,
          haccp_attributes: recipeData?.haccp_attributes,
        };
      default:
        return {};
    }
  };

  // Helper: check if there are unsaved changes
  const hasUnsavedChanges = () => {
    if (!oldRecipeDatas) return false;
    const currentSection = formState.activeSection;
    const currentSectionData = getSectionData(
      formState,
      currentSection,
      updatedData
    );
    const oldSectionData = getSectionData(
      oldRecipeDatas,
      currentSection,
      updatedData
    );
    return (
      JSON.stringify(currentSectionData) !== JSON.stringify(oldSectionData)
    );
  };

  const isSectionCompleted = (sectionId) => {
    if (!updatedData) return false;

    switch (sectionId) {
      case 'basic-info':
        return !!updatedData?.recipe_title?.trim();

      case 'media':
        return !!updatedData?.recipe_placeholder;

      case 'ingredients':
        return !!(updatedData?.ingredients?.length > 0);

      case 'instructions':
        return !!(updatedData?.steps?.length > 0);

      case 'nutrition':
        // Nutrition is optional, consider completed if any nutrition data is provided
        return !!(
          updatedData?.nutrition_attributes?.length > 0 ||
          updatedData?.allergen_attributes?.contains?.length > 0 ||
          updatedData?.allergen_attributes?.may_contain?.length > 0
        );

      case 'serving':
        return !!(
          updatedData?.recipe_yield > 0 &&
          updatedData?.recipe_total_portions > 0
        );

      case 'haccp':
        // HACCP is optional, consider completed if any HACCP data is provided
        return !!(updatedData?.haccp_attributes?.length > 0);

      default:
        return false;
    }
  };

  const sectionDefinitions = [
    {
      id: 'basic-info',
      title: 'Basic Information',
      icon: 'FileText',
      component: BasicInfoSection,
      required: true,
    },
    {
      id: 'media',
      title: 'Media & Photos',
      icon: 'Camera',
      component: MediaUploadSection,
      required: true,
    },
    {
      id: 'ingredients',
      title: 'Ingredients',
      icon: 'ShoppingCart',
      component: IngredientsSection,
      required: true,
    },
    {
      id: 'nutrition',
      title: 'Nutrition & Allergens',
      icon: 'Heart',
      component: NutritionSection,
      required: false,
    },
    {
      id: 'instructions',
      title: 'Instructions',
      icon: 'List',
      component: InstructionsSection,
      required: false,
    },
    {
      id: 'serving',
      title: 'Serving Details',
      icon: 'Users',
      component: ServingDetailsSection,
      required: false,
    },
    {
      id: 'haccp',
      title: 'HACCP',
      icon: 'BadgeAlert',
      component: HACCPSection,
      required: false,
    },
  ];

  // Helper: check if all required fields in all required sections are filled
  const checkAllRequiedValidation = () => {
    // Check for each required section in sectionDefinitions
    for (const section of sectionDefinitions) {
      if (section.required) {
        switch (section.id) {
          case 'basic-info':
            if (!formState.basicInfo.recipeName?.trim()) return false;
            break;
          case 'media':
            if (!formState.media.mainImage) return false;
            break;
          case 'ingredients':
            if (!formState.ingredients || formState.ingredients.length === 0)
              return false;
            break;
          default:
            break;
        }
      }
    }
    return true;
  };

  // New: Check if all required sections are completed
  const allRequiredCompleted = sectionDefinitions
    .filter((section) => section?.required)
    .every((section) => isSectionCompleted(section?.id));

  // Only required sections are considered for enabling/disabling
  const formSections = sectionDefinitions.map((section, idx, arr) => {
    if (!section.required) {
      // Non-required sections: disable if any required section is not completed
      return { ...section, isDisabled: !allRequiredCompleted };
    }
    if (idx === 0) {
      // First required section is always enabled
      return { ...section, isDisabled: false };
    }
    // Enable this required section only if the previous required section is completed
    const prevRequiredSections = arr?.filter((s, i) => s?.required && i < idx);
    const prevSectionId =
      prevRequiredSections?.length > 0
        ? prevRequiredSections[prevRequiredSections?.length - 1]?.id
        : null;
    return {
      ...section,
      isDisabled: prevSectionId ? !isSectionCompleted(prevSectionId) : false,
    };
  });

  const handleSectionChange = (sectionId) => {
    if (hasUnsavedChanges()) {
      setBackButtonModal(true);
    } else {
      dispatch({ type: 'SET_ACTIVE_SECTION', payload: sectionId });
    }
  };

  // Refactored: Section-wise validation
  const checkSingleRequiredValidate = () => {
    const errors = {};

    switch (formState.activeSection) {
      case 'basic-info':
        if (!formState.basicInfo.recipeName?.trim()) {
          errors['basic-info'] = true;
          errors.recipeName = 'Recipe name is required';
        }
        break;
      case 'media':
        if (!formState.media.mainImage) {
          errors['media'] = true;
          errors.mainImage = 'Main image is required';
        }
        break;
      case 'ingredients':
        if (!formState.ingredients || formState.ingredients.length === 0) {
          errors.ingredients = 'At least one ingredient is required';
        }
        break;
      default:
        break;
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handlePreview = () => {
    if (checkSingleRequiredValidate()) {
      // Navigate to preview with form data
      // router.push({
      //   pathname: '/recipes/recipe-preview',
      //   query: { previewDetails: JSON.stringify(formState) },
      // });
      router.push(
        `/recipes/recipe-preview/${oldRecipeDatas?.recipe_slug || oldRecipeDatas?.recipeId}`
      );
    }
  };

  // Function to prepare recipe data for API
  const prepareRecipeData = (state) => {
    if (!state) {
      return {};
    }

    const allMediaFiles = [
      ...(state.media.additionalImages || []),
      ...(state.media.documents || []),
      ...(state.media.audio || []),
    ];

    const mediaLinks = state?.media?.links?.map((link) => {
      return {
        type: link?.type,
        item_link: link?.url,
        item_link_type: link.item_link_type,
      };
    });

    const ingredientsList = state?.ingredients
      ?.map((ing) => {
        return {
          id: ing?.id,
          quantity: ing?.quantity,
          measure: ing?.measure_of_cost?.id,
          wastage: ing?.ingredient_wastage,
          cost: ing?.cost,
          cooking_method: state?.isCookingMethod ? ing?.cookingMethod : null,
          preparation_method: state?.isPreparationMethod
            ? ing?.preparationMethod
            : null,
        };
      })
      ?.filter((ing) => ing?.id !== 'nutrition-container');

    const instructionsList = state?.instructions?.map((step) => {
      return {
        recipe_step_order: step?.stepNumber,
        recipe_step_description: step?.description,
        item_id: step?.image?.id,
      };
    });

    const instrucationImages = state?.instructions?.map((step) => {
      return step?.image?.file;
    });
    const nutritionList = state?.nutrition?.nutrition?.map((nut) => {
      return {
        id: nut?.id,
        unit: nut?.value,
        unit_of_measure: nut?.unit_of_measure,
        description: nut?.description || '',
      };
    });

    // Process HACCP data
    const haccpList =
      state?.haccp?.sections?.map((section) => {
        return {
          id: section?.haccp_id,
          attribute_description: section?.description,
          use_default: section?.useDefault || false,
        };
      }) || [];

    // Process recipe files - ensure we're getting the actual files
    const recipeFiles = allMediaFiles
      ?.filter((file) => file?.file) // Only include files that have the file property
      ?.map((file) => file?.file); // Extract the actual file object

    const availableMedia = allMediaFiles?.map((item) => ({
      item_id: item?.id,
      type: 'item',
    }));

    const contains = [...new Set(state?.nutrition?.commonAllergens || [])];

    const mayContainRaw = state?.nutrition?.mayContainAllergens || [];

    // Filter out any value in mayContain that already exists in contains
    const may_contain = mayContainRaw.filter(
      (item) => !contains.includes(item)
    );

    return {
      recipe_title: state.basicInfo.recipeName,
      recipe_public_title: state.basicInfo.publicDisplayName,
      recipe_description: state.basicInfo.recipeDescription,
      categories: state.basicInfo.categories?.map((cat) => cat.id || cat) || [],
      dietary_attributes:
        state.basicInfo.dietaries?.map((diet) => diet.id || diet) || [],
      recipe_preparation_time: state.basicInfo.prepTime,
      recipe_cook_time: state.basicInfo.cookTime,
      has_recipe_public_visibility:
        state.basicInfo.visibility.includes('public'),
      has_recipe_private_visibility:
        state.basicInfo.visibility.includes('private'),
      recipePlaceholder: state?.media?.mainImage?.id || null,
      recipeFiles: recipeFiles || [], // Use the processed recipeFiles
      resources: [...(mediaLinks || []), ...(availableMedia || [])] || [],
      recipe_yield: state.serving.yield.value,
      recipe_yield_unit: state.serving.yield.unit,
      recipe_total_portions: state.serving.totalPortions,
      recipe_single_portion_size: state.serving.singlePortionSize,
      recipe_serving_method: state.serving.servingMethod,
      recipe_serve_in: state.serving.serveIn,
      recipe_garnish: state.serving.garnish,
      recipe_foh_tips: state.serving.fohTips,
      recipe_head_chef_tips: state.serving.chefTips,
      ingredients: ingredientsList || [],
      instructions: state.instructions || [],
      is_cooking_method: state.isCookingMethod || false,
      is_preparation_method: state.isPreparationMethod || false,
      recipe_complexity_level: state.basicInfo.complexity_level || 'low',
      steps: instructionsList || [],
      stepImages: instrucationImages || [],
      nutrition_attributes: nutritionList || [],
      allergen_attributes: {
        contains: contains,
        may_contain: may_contain,
      },
      haccp_use_from: state?.haccp?.useFrom?.value || null,
      haccp_sections: haccpList || [],
      is_cost_manual: state?.is_cost_manual || false,
    };
  };

  const handleOpenSaveAndExitModal = async () => {
    if (checkAllRequiedValidation()) {
      setShowExitModal(true);
      setBackButtonModal(false);
    } else if (checkSingleRequiredValidate()) {
      await handleSaveAndExit('draft');
    }
  };

  const handleCloseSaveAndExitModal = () => {
    setShowExitModal(false);
  };

  const handleSaveAndExit = async (recipeStatus) => {
    const recipeData = prepareRecipeData(formState);
    // formData.append('recipe_status', recipeStatus);

    const nutritionList = formState?.ingredients
      ?.flatMap((ing) => ing.nutrition || [])
      ?.map((nut) => {
        return {
          id: nut?.id,
          unit: nut?.value,
          unit_of_measure: nut?.unit_of_measure,
          description: nut?.description || '',
        };
      });

    const contains = [...new Set(formState?.nutrition?.commonAllergens || [])];

    const mayContainRaw = formState?.nutrition?.mayContainAllergens || [];

    // Filter out any value in mayContain that already exists in contains
    const may_contain = mayContainRaw.filter(
      (item) => !contains.includes(item)
    );

    let sendData = '';

    if (formState.activeSection === 'basic-info') {
      sendData = {
        ...(updatedData?.recipe_id && { recipe_id: updatedData?.recipe_id }),
        recipe_title: recipeData?.recipe_title,
        recipe_public_title: recipeData?.recipe_public_title,
        recipe_description: recipeData?.recipe_description,
        recipe_preparation_time: recipeData?.recipe_preparation_time * 60,
        recipe_cook_time: recipeData?.recipe_cook_time * 60,
        has_recipe_public_visibility: recipeData?.has_recipe_public_visibility,
        has_recipe_private_visibility:
          recipeData?.has_recipe_private_visibility,
        recipe_status: recipeStatus,
        categories: recipeData?.categories,
        recipe_complexity_level: recipeData?.recipe_complexity_level,
        dietary_attributes: recipeData?.dietary_attributes,
      };
    } else if (formState.activeSection === 'media') {
      sendData = {
        recipe_id: updatedData?.recipe_id,
        recipe_resources: recipeData?.resources,
        recipe_placeholder: recipeData?.recipePlaceholder,
        recipe_status: recipeStatus,
      };
    } else if (formState.activeSection === 'ingredients') {
      sendData = {
        recipe_id: updatedData?.recipe_id,
        ingredients: recipeData?.ingredients,
        nutrition_attributes: nutritionList,
        allergen_attributes: {
          contains: contains,
          may_contain: may_contain,
        },
        is_ingredient_cooking_method: recipeData?.is_cooking_method,
        is_preparation_method: recipeData?.is_preparation_method,
        is_cost_manual: recipeData?.is_cost_manual,
        recipe_status: recipeStatus,
      };
    } else if (formState.activeSection === 'nutrition') {
      sendData = {
        recipe_id: updatedData?.recipe_id,
        nutrition_attributes: recipeData?.nutrition_attributes,
        allergen_attributes: {
          contains: recipeData?.allergen_attributes?.contains,
          may_contain: recipeData?.allergen_attributes?.may_contain,
        },
        recipe_status: recipeStatus,
      };
    } else if (formState.activeSection === 'serving') {
      sendData = {
        recipe_id: updatedData?.recipe_id,
        recipe_yield: recipeData?.recipe_yield,
        recipe_yield_unit: recipeData?.recipe_yield_unit,
        recipe_total_portions: recipeData?.recipe_total_portions,
        recipe_single_portion_size: recipeData?.recipe_single_portion_size,
        recipe_serving_method: recipeData?.recipe_serving_method,
        recipe_serve_in: recipeData?.recipe_serve_in,
        recipe_garnish: recipeData?.recipe_garnish,
        recipe_foh_tips: recipeData?.recipe_foh_tips,
        recipe_head_chef_tips: recipeData?.recipe_head_chef_tips,
        recipe_status: recipeStatus,
      };
    } else if (formState.activeSection === 'haccp') {
      sendData = {
        recipe_id: updatedData?.recipe_id,
        haccp_attributes: recipeData?.haccp_sections,
        recipe_status: recipeStatus,
      };
    } else if (formState.activeSection === 'instructions') {
      sendData = {
        recipe_id: updatedData?.recipe_id,
        recipe_steps: recipeData?.steps,
        recipe_status: recipeStatus,
      };
    }

    let RECIPE_URL = '';
    if (formState.activeSection === 'basic-info') {
      RECIPE_URL = RECIPE_URLS.RECIPE_BASIC_INFO;
    } else if (
      ['ingredients', 'nutrition', 'serving', 'haccp'].includes(
        formState.activeSection
      )
    ) {
      RECIPE_URL = RECIPE_URLS.INGREDIENTS_NUTRITION;
    } else if (formState.activeSection === 'media') {
      RECIPE_URL = RECIPE_URLS.ADD_RECIPE_FILES;
    } else if (formState.activeSection === 'instructions') {
      RECIPE_URL = RECIPE_URLS.RECIPE_STEPS;
    }
    try {
      setIsSaving(true);
      // const recipeData = prepareRecipeData(formState);
      // const formData = prepareFormData(recipeData); // Convert to FormData
      // formData.append('recipe_status', recipeStatus);

      const response = await createUpdateRecipe(RECIPE_URL, sendData);
      const updatedResponse = {
        recipe_id: response?.data?.recipe_id,
        recipe_slug: response?.data?.recipe_slug,
        recipe_title: recipeData?.recipe_title,
        recipe_placeholder: recipeData?.recipePlaceholder,
        ingredients: recipeData?.ingredients,
        steps: recipeData?.steps,
        nutrition_attributes: recipeData?.nutrition_attributes || nutritionList,
        allergen_attributes: {
          contains: recipeData?.allergen_attributes?.contains || contains,
          may_contain:
            recipeData?.allergen_attributes?.may_contain || may_contain,
        },
        recipe_yield: recipeData?.recipe_yield,
        recipe_total_portions: recipeData?.recipe_total_portions,
        haccp_attributes: recipeData?.haccp_attributes,
      };
      saveToStorage(identifiers.RECIPE_CREATION_DATA, updatedResponse || '');
      // if (isUpdate) {
      //   // Update existing recipe
      //   response = await updateRecipe(formState.recipeId, formData);
      // } else {
      //   // Create new recipe with FormData
      //   response = await createRecipe(formData);
      // }
      if (response) {
        setOldRecipeDatas(JSON.parse(JSON.stringify(formState)));
        formState.recipeStatus = recipeStatus;
        formState.lastUpdated = new Date().toISOString();
        setShowExitModal(false);
        setBackButtonModal(false);
        setApiMessage(
          'success',
          response?.message || 'Recipe saved successfully!'
        );
        if (recipeStatus === 'publish') {
          router.push('/recipes');
        } else if (recipeStatus === 'draft') {
          // Move to next section if available
          const currentIdx = formSections.findIndex(
            (section) => section.id === formState.activeSection
          );
          if (currentIdx !== -1 && currentIdx < formSections.length - 1) {
            const nextSection = formSections[currentIdx + 1];
            dispatch({ type: 'SET_ACTIVE_SECTION', payload: nextSection.id });
          }
        }
      }
    } catch (error) {
      setShowExitModal(false);
      setBackButtonModal(false);
      setApiMessage(
        'error',
        error?.response?.data?.message || 'Failed to save recipe'
      );
    } finally {
      setShowExitModal(false);
      setBackButtonModal(false);
      setIsSaving(false);
    }
  };

  const currentSection = formSections.find(
    (section) => section.id === formState.activeSection
  );
  const CurrentSectionComponent = currentSection?.component;

  return (
    <>
      {(isSaving || isLoading) && <PreLoader />}
      <div className="recipe-page-header">
        <TopBackPageButton
          title={`${isUpdate ? 'Update' : 'Add'} Recipe`}
          onBackClick={handleRedirect}
          className={isUpdate ? 'update-recipe' : ''}
          rightActions={
            isUpdate && (
              <div className="recipe-page-header__actions">
                <Tooltip
                  title={
                    <Typography className="sub-title-text">
                      Staff Tracking
                    </Typography>
                  }
                  arrow
                  classes={{
                    tooltip: 'info-tooltip-container',
                  }}
                >
                  <span>
                    <CustomButton
                      variant="outlined"
                      isIconOnly
                      startIcon={<Icon name="List" size={18} />}
                      onClick={() =>
                        router.push(
                          `/recipes/staff-tracking?recipeId=${formState.recipeId || 'demo'}`
                        )
                      }
                      className="staff-tracking-btn"
                    />
                  </span>
                </Tooltip>
                <Tooltip
                  title={
                    <Typography className="sub-title-text">Refresh</Typography>
                  }
                  arrow
                  classes={{
                    tooltip: 'info-tooltip-container',
                  }}
                >
                  <span>
                    <CustomButton
                      variant="outlined"
                      isIconOnly
                      startIcon={<Icon name="RotateCcw" size={18} />}
                      onClick={() => {
                        // Reset functionality - you can implement this as needed
                        window.location.reload();
                      }}
                      className="reset-btn"
                    />
                  </span>
                </Tooltip>
              </div>
            )
          }
        />
      </div>
      {/* Form Progress Indicator */}
      <Box className="section-right-content add-edit-recipe-container">
        <FormProgressIndicator
          formState={formState}
          validationErrors={validationErrors}
        />

        {/* Main Content */}
        <div className="add-edit-recipe__layout">
          {/* Desktop: Section Navigation Sidebar */}
          <SectionNavigationSidebar
            formSections={formSections}
            activeSection={formState.activeSection}
            onSectionChange={handleSectionChange}
            validationErrors={validationErrors}
            calculations={formState.calculations}
            currency={currency}
            isMobileView={isMobileView}
          />

          {/* Main Form Content */}
          <MainFormContent
            formSections={formSections}
            activeSection={formState.activeSection}
            onSectionChange={handleSectionChange}
            currentSection={currentSection}
            CurrentSectionComponent={CurrentSectionComponent}
            formState={formState}
            dispatch={dispatch}
            validationErrors={validationErrors}
            setValidationErrors={setValidationErrors}
            isMobileView={isMobileView}
            currency={currency}
            isUpdate={isUpdate}
            onPreview={handlePreview}
            onSaveAndExit={handleOpenSaveAndExitModal}
            checkAllRequiedValidation={checkAllRequiedValidation}
          />
        </div>
      </Box>
      <DialogBox
        open={showExitModal}
        handleClose={handleCloseSaveAndExitModal}
        title="Confirmation"
        className="confirmation-modal"
        dividerClass="confirmation-modal-divider"
        content={
          <ConfirmationModal
            handleCancel={() => handleSaveAndExit('draft')}
            handleConfirm={() => handleSaveAndExit('publish')}
            text="Do you want to save your recipe before exiting? Your progress will be preserved."
            cancelText="Save & Next"
            confirmText="Published & Exit"
            isLoading={isSaving}
          />
        }
      />

      <DialogBox
        open={backButtonModal}
        handleClose={() => setBackButtonModal(false)}
        title="You have unsaved changes."
        className="delete-modal"
        dividerClass="delete-modal-divider"
        content={
          <DeleteModal
            handleCancel={() => handleOpenBackModal()}
            handleConfirm={() => router.push('/recipes')}
            text="Would you like to save your recipe before exiting, or discard your changes?"
            cancelText="Save & Exit"
            confirmText="Discard"
            isLoading={isSaving}
          />
        }
      />
    </>
  );
}
