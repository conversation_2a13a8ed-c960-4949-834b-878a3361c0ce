.attachment-wrap {
  max-height: calc(100vh - 200px - var(--banner-height));
  overflow: scroll;

  .attachment-history-wrap {
    position: relative;

    .devider-wrap {
      padding: var(--spacing-none) var(--spacing-lg) var(--spacing-none)
        var(--spacing-5xl);
    }

    .attachment-item {
      .header-date-wrap {
        padding-top: var(--spacing-sm);
        position: sticky;
        top: var(--spacing-none);
        background: var(--color-white);
        z-index: 9;

        .header-date {
          font-weight: var(--font-weight-medium);
        }

        .calender-icon {
          background-color: var(--color-white);
          position: absolute;
          left: -24.5px;
          fill: var(--icon-color-primary);
          height: var(--icon-size-xs);
          width: var(--icon-size-xs);
          border-radius: var(--border-radius-xs);
          padding: var(--spacing-xxs);
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
          border: 1px solid var(--color-light-grayish-blue);
        }
      }

      .name-text-wrap {
        margin-top: var(--spacing-md);
        .file-status-wrap {
          font-weight: var(--font-weight-medium);
        }

        .circle-wrap {
          position: absolute;
          left: 49.5px;
          fill: var(--icon-color-primary);
          height: var(--spacing-xs);
          width: var(--spacing-xs);
          border-radius: 50%;
          background-color: var(--icon-color-primary);
          box-shadow:
            0 0 0 3px var(--color-white),
            0 0 0 4px var(--color-light-grayish-blue);
        }
      }

      .file-name {
        font-size: var(--font-size-sm);
        margin-bottom: var(--spacing-xs);

        .file-name-text {
          color: var(--text-color-slate-gray);
          display: inline-block;
          padding-right: var(--spacing-sm);
        }

        .note-content {
          color: var(--text-color-black);
          font-size: var(--font-size-sm);
          font-weight: var(--font-weight-regular);
        }
      }

      .time-wrap {
        padding: var(--spacing-xs) var(--spacing-none);
        font-size: var(--font-size-xs);
        color: var(--text-color-slate-gray);
        font-weight: var(--font-weight-regular);
        opacity: 0.8;
      }

      .attachment-type-wrap {
        font-size: var(--font-size-sm);
        display: flex;
        align-items: center;
        gap: var(--spacing-xs);

        .attachment-type-text {
          display: inline-block;
          color: var(--text-color-slate-gray);
        }
      }
    }
  }
}
