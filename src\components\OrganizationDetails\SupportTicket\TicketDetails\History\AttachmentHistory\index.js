import React from 'react';
import { Box, Divider, Typography } from '@mui/material';
import CalendarMonthIcon from '@mui/icons-material/CalendarMonth';
import CircleIcon from '@mui/icons-material/Circle';
import { DateFormat } from '@/helper/common/commonFunctions';
import './attachmenthistory.scss';

const AttachmentHistory = ({ attachmentData }) => {
  // Helper function to format action type text
  const formatActionType = (actionType) => {
    if (!actionType) return '';
    return actionType
      .split('_')
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' ');
  };

  // Helper function to get status class based on status value using global classes
  const getStatusClass = (status) => {
    if (!status) return 'draft';
    const statusLower = status.toLowerCase();
    const statusMap = {
      open: 'status-yellow',
      'in progress': 'draft',
      in_progress: 'draft',
      ongoing: 'ongoing',
      resolved: 'success',
      closed: 'closed',
      completed: 'success',
      active: 'status-active',
      inactive: 'status-inactive',
      pending: 'draft',
      approved: 'success',
      rejected: 'status-inactive',
      cancelled: 'status-inactive',
    };
    return statusMap[statusLower] || 'draft';
  };

  // Helper function to format status display text
  const formatStatusText = (status) => {
    if (!status) return '';
    return status
      .split('_')
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' ');
  };
  return (
    <Box className="attachment-wrap">
      <Box className="devider-wrap">
        <Divider orientation="vertical" className="vertical-divider" />
      </Box>
      <Box className="attachment-history-wrap">
        {attachmentData?.map((entry, index) => (
          <Box key={index} className="d-flex attachment-history">
            <Box className="devider-wrap">
              <Divider orientation="vertical" />
            </Box>
            <Box className="attachment-item">
              <Box className="d-flex align-center header-date-wrap">
                <CalendarMonthIcon className="calender-icon" />
                <Typography component="p" className="header-date title-text">
                  {DateFormat(entry?.created_at, 'datesWithhour')}
                </Typography>
              </Box>
              <Box className="d-flex align-center name-text-wrap">
                <CircleIcon className="circle-wrap" />
                <Typography className="file-status-wrap title-text">
                  {formatActionType(entry?.action_type)}
                </Typography>
              </Box>
              {entry?.change_note && (
                <Typography className="file-name body-text">
                  <span className="file-name-text content-text">Note </span>
                  <span className="note-content sub-title-text">
                    {entry?.change_note}
                  </span>
                </Typography>
              )}
              {entry?.new_status && (
                <Typography className="attachment-type-wrap body-text">
                  <span className="attachment-type-text content-text">
                    Status
                  </span>
                  <span
                    className={`content-text-sm ${getStatusClass(entry?.new_status)}`}
                  >
                    {formatStatusText(entry?.new_status)}
                  </span>
                </Typography>
              )}
            </Box>
          </Box>
        ))}
      </Box>
    </Box>
  );
};

export default AttachmentHistory;
