'use client';

import React, { useState, useEffect } from 'react';
import { Box, Typography, Divider } from '@mui/material';
import { useRouter } from 'next/navigation';
import SimpleStatsCard from './components/SimpleStatsCard';
import RecentTickets from './components/RecentTickets';
import ContentLoader from '@/components/UI/ContentLoader';
import { supportTicketService } from '@/services/supportTicketService';
import {
  setApiMessage,
  checkOrganizationRole,
} from '@/helper/common/commonFunctions';
import './supportticketdashboard.scss';

const SupportTicketDashboard = () => {
  const router = useRouter();
  const [dashboardData, setDashboardData] = useState({
    overview: {
      total_tickets: 0,
      unassigned_tickets: 0,
      in_progress_tickets: 0,
      avg_response_time_hours: 0,
    },
    statistics: {
      by_status: [],
      by_priority: [],
      by_module: [],
    },
    recent_activity: [],
  });
  const [loading, setLoading] = useState(true);

  // Check if user has super_admin role for dashboard access
  useEffect(() => {
    const isSuperAdmin = checkOrganizationRole('super_admin') || false;
    if (!isSuperAdmin) {
      // Redirect to all tickets if user is not super_admin
      router.push('/support-ticket/all-tickets');
      return;
    }
  }, [router]);

  // Fetch dashboard data from API
  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true);
        const data = await supportTicketService.getDashboardData();

        if (data) {
          // Update dashboard data with new API structure
          setDashboardData({
            overview: {
              total_tickets: data?.overview?.total_tickets ?? 0,
              unassigned_tickets: data?.overview?.unassigned_tickets ?? 0,
              in_progress_tickets: data?.overview?.in_progress_tickets ?? 0,
              avg_response_time_hours:
                data?.overview?.avg_response_time_hours ?? 0,
            },
            statistics: {
              by_status: data?.statistics?.by_status ?? [],
              by_priority: data?.statistics?.by_priority ?? [],
              by_module: data?.statistics?.by_module ?? [],
            },
            recent_activity: data?.recent_activity ?? [],
          });
        }
      } catch (error) {
        console.error('Error fetching dashboard data:', error);
        setApiMessage('error', 'Failed to load dashboard data');

        // Reset to empty state on error
        setDashboardData({
          overview: {
            total_tickets: 0,
            unassigned_tickets: 0,
            in_progress_tickets: 0,
            avg_response_time_hours: 0,
          },
          statistics: {
            by_status: [],
            by_priority: [],
            by_module: [],
          },
          recent_activity: [],
        });
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  // Simple statistics data - clean design using new overview structure
  const statisticsData = [
    {
      id: 'total-tickets',
      title: 'Total Tickets',
      value: dashboardData.overview.total_tickets.toString(),
      icon: 'BarChart3',
      color: 'primary',
    },
    {
      id: 'unassigned-tickets',
      title: 'Unassigned Tickets',
      value: dashboardData.overview.unassigned_tickets.toString(),
      icon: 'AlertCircle',
      color: 'warning',
    },
    {
      id: 'in-progress-tickets',
      title: 'In Progress Tickets',
      value: dashboardData.overview.in_progress_tickets.toString(),
      icon: 'Clock',
      color: 'accent',
    },
    {
      id: 'avg-response-time',
      title: 'Avg Response Time',
      value: `${dashboardData.overview.avg_response_time_hours}h`,
      icon: 'CheckCircle',
      color: 'success',
    },
  ];

  // Transform recent activity data to match component expectations
  const transformRecentActivity = (recentActivity) => {
    return (
      recentActivity?.map((ticket) => ({
        id: ticket?.id,
        title: ticket?.ticket_title,
        status: ticket?.ticket_status,
        priority: ticket?.ticket_priority,
        date: ticket?.created_at,
        slug: ticket?.ticket_slug,
      })) ?? []
    );
  };

  // Get urgent tickets (tickets with high priority)
  const getUrgentTickets = () => {
    return (
      dashboardData?.recent_activity
        ?.filter(
          (ticket) =>
            ticket?.ticket_priority === 'high' ||
            ticket?.ticket_priority === 'urgent'
        )
        ?.slice(0, 5) ?? []
    );
  };

  const handleStatisticClick = () => {
    // Handle navigation or filtering based on statistic
    // Could navigate to filtered tickets view
  };

  // Transform data for components
  const recentTicketsData = transformRecentActivity(
    dashboardData?.recent_activity
  );
  const urgentTicketsData = transformRecentActivity(getUrgentTickets());

  return (
    <Box className="section-wrapper">
      <Box className="section-right">
        <Box className="section-right-tab-header">
          <Box className="d-flex align-center justify-space-between">
            <Box className="section-right-title d-flex align-center gap-sm">
              <Typography className="sub-header-text">Dashboard</Typography>
            </Box>
          </Box>
          <Divider />
        </Box>

        <Box className="section-right-content">
          <Box className="support-ticket-dashboard-wrap">
            <Box className="support-ticket-dashboard">
              {/* Header */}
              <Box className="support-ticket-dashboard__header">
                <Typography className="body-text">
                  Overview of your support ticket system
                </Typography>
              </Box>

              {loading ? (
                <ContentLoader />
              ) : (
                <>
                  {/* Statistics Grid */}
                  <Box className="support-ticket-dashboard__stats-grid">
                    {statisticsData.map((stat) => (
                      <SimpleStatsCard
                        key={stat.id}
                        {...stat}
                        onClick={handleStatisticClick}
                      />
                    ))}
                  </Box>

                  {/* Content Grid */}
                  <Box className="support-ticket-dashboard__content-grid">
                    {/* Recent Tickets */}
                    <Box className="support-ticket-dashboard__recent-tickets">
                      <RecentTickets
                        tickets={recentTicketsData}
                        title="Recent Tickets"
                        emptyMessage="No recent tickets found"
                        showPriority={true}
                        className="recent-tickets"
                      />
                    </Box>

                    {/* Urgent Tickets */}
                    <Box className="support-ticket-dashboard__urgent-tickets">
                      <RecentTickets
                        tickets={urgentTicketsData}
                        title="Urgent Tickets"
                        emptyMessage="No urgent tickets found"
                        showPriority={false}
                        className="urgent-tickets"
                      />
                    </Box>
                  </Box>
                </>
              )}
            </Box>
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export default SupportTicketDashboard;
