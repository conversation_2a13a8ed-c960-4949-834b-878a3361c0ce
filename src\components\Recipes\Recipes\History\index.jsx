'use client';

import React, { useEffect, useState } from 'react';
import { Box, Divider, Typography } from '@mui/material';
import Icon from '@/components/UI/AppIcon/AppIcon';
import { DateFormat, setApiMessage } from '@/helper/common/commonFunctions';
import PreLoader from '@/components/UI/Loader';
import { useParams, useRouter } from 'next/navigation';
import ArrowBackIosIcon from '@mui/icons-material/ArrowBackIos';
import InfiniteScroll from 'react-infinite-scroll-component';

import { getRecipeHistory } from '@/services/recipeService';
import NoDataView from '@/components/UI/NoDataView';
import ContentLoader from '@/components/UI/ContentLoader';
import CommonNoDataImage from '../CommonNoDataImage';
import './history.scss';

export default function RecipeHistory({ isView }) {
  const router = useRouter();
  const { slug } = useParams();
  const [recipeData, setRecipeData] = useState([]);
  const [loader, setLoader] = useState(false);
  // const [recipeVersionData, setRecipeVersionData] = useState(null);

  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);

  // GET RECIPE HISTORY
  const getRecipeHistoryData = async (pageNumber = 1) => {
    recipeData?.length === 0 && setLoader(true);
    try {
      const response = await getRecipeHistory(slug, pageNumber, 10, isView);
      const historyData = response.history;

      setRecipeData((prevData) => [...prevData, ...historyData]);
      setHasMore(historyData?.length > 0);
      setLoader(false);
    } catch (error) {
      setLoader(false);
      setRecipeData([]);
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  useEffect(() => {
    getRecipeHistoryData(page);
  }, [page]);

  // const handleViewVersion = (item) => {
  //   setRecipeVersionData(item);
  //   if (isView === 'activity') {
  //     router.push(`/recipes/recipe-activity/${slug}?version=${item?.id}`);
  //   } else {
  //     router.push(`/recipes/recipe-history/${slug}?version=${item?.id}`);
  //   }
  // };

  const fetchMoreData = () => {
    setPage((prevPage) => prevPage + 1);
  };

  // const latestVersion = recipeVersionData || recipeData?.[0];
  const latestVersion = recipeData?.[0];

  return (
    <>
      <Box className="d-flex align-center version-title-header">
        <ArrowBackIosIcon
          className="cursor-pointer mt4"
          onClick={() => {
            router.push('/recipes');
          }}
        />
        <Box>
          <Typography className="sub-header-text">
            {latestVersion?.recipe_title}
          </Typography>
          <Typography className="content-text">
            Showing all {isView} for this recipe
          </Typography>
        </Box>
      </Box>
      <Divider className="" />
      <Box className="section-right-content recipe-history-main-container">
        <Box
          className="recipe-history-container"
          id="recipe-history-scrollable"
        >
          {loader ? (
            <PreLoader />
          ) : recipeData.length === 0 ? (
            <NoDataView
              image={<CommonNoDataImage />}
              title="No recipe history found"
              description="There is no recipe history available at the moment."
              className="no-data-auto-margin-height-conainer"
            />
          ) : (
            <Box>
              {/* History Cards */}
              <Box className="recipe-history-cards-container">
                <InfiniteScroll
                  dataLength={recipeData?.length}
                  next={fetchMoreData}
                  hasMore={hasMore}
                  loader={
                    <Box>
                      <ContentLoader />
                    </Box>
                  }
                  endMessage={
                    recipeData?.length > 0 && (
                      <div className="recipes-history-end-message">
                        <p>You've seen all recipe {isView}!</p>
                      </div>
                    )
                  }
                  scrollableTarget="recipe-history-scrollable"
                >
                  {recipeData &&
                    recipeData?.map((item, index) => {
                      return (
                        <Box
                          key={item?.id || index}
                          className={`recipe-history-card mb16`}
                          // className={`recipe-history-card mb16 ${
                          //   item?.id === latestVersion?.id ? 'active' : ''
                          // }`}
                          // onClick={() => handleViewVersion(item)}
                        >
                          {/* Card Header */}
                          <Box className="d-flex justify-space-between align-center recipe-history-card-header">
                            <Box className="d-flex align-center gap-sm">
                              <Typography className="recipe-history-card-title">
                                {/* {item?.recipe_title} */}
                                {item?.action
                                  ? item.action
                                      .replace(/_/g, ' ')
                                      .replace(/\b\w/g, (l) => l.toUpperCase())
                                  : 'Update'}
                              </Typography>
                              {/* <Typography className="content-text">
                                {isView === 'activity' ? 'Activity' : 'History'}
                              </Typography> */}
                            </Box>
                          </Box>

                          {/* Card Content */}
                          <Box className="recipe-history-card-content">
                            <Typography className="recipe-history-card-changes">
                              {item?.changes || 'No changes recorded'}
                            </Typography>
                          </Box>

                          {/* Card Footer */}
                          <Box className="d-flex justify-space-between align-center recipe-history-card-footer">
                            <Box className="d-flex align-center gap-sm">
                              <Icon name="User" size={16} />
                              <Typography className="recipe-history-card-user">
                                {item?.user || 'Unknown User'}
                              </Typography>
                              <Typography className="recipe-history-card-date">
                                {item?.date ||
                                  (item?.created_at
                                    ? DateFormat(
                                        item?.created_at,
                                        'datesWithhour'
                                      )
                                    : '')}
                              </Typography>
                            </Box>
                            {/* <Typography className="recipe-history-card-action">
                              {item?.action
                                ? item.action
                                    .replace(/_/g, ' ')
                                    .replace(/\b\w/g, (l) => l.toUpperCase())
                                : 'Update'}
                            </Typography> */}
                          </Box>
                        </Box>
                      );
                    })}
                </InfiniteScroll>
              </Box>
            </Box>
          )}
        </Box>
      </Box>
    </>
  );
}
