.attachment-wrap {
  .empty-attachment {
    padding: var(--spacing-lg) var(--spacing-none);

    .no-attachment {
      font-family: var(--font-family-primary);
    }

    .upload-attachment {
      font-family: var(--font-family-primary);
    }

    .browse-files-wrap {
      padding-top: var(--spacing-lg);

      .browse-files {
        padding: var(--spacing-xs) var(--spacing-lg) !important;
        font-size: var(--font-size-base) !important;
        font-weight: var(--font-weight-medium);

        &:hover {
          color: var(--color-white) !important;
        }
      }
    }
  }

  .media-previews {
    .add-file-wrap {
      border-bottom: var(--border-width-xs) solid var(--border-color-light-gray);
      padding: var(--spacing-md) var(--spacing-none);

      .add-file {
        font-size: var(--font-size-base) !important;
        font-weight: var(--font-weight-regular);
        background-color: transparent;
        color: var(--color-dark-50) !important;
        padding: var(--spacing-none) !important;
        border: none;

        &:hover {
          color: var(--color-dark) !important;
          box-shadow: none !important;
        }

        @media (max-width: 575px) {
          font-size: var(--font-size-sm) !important;
        }
      }
    }

    .preview-container {
      .file-name-wrap {
        @media (max-width: 1024px) {
          gap: var(--spacing-md) !important;
        }

        @media (max-width: 575px) {
          gap: var(--spacing-xs) !important;
        }
      }

      .file-name {
        @media (max-width: 1305px) {
          text-overflow: ellipsis;
          white-space: nowrap;
          overflow: hidden;
          cursor: pointer;
          max-width: 40ch;
        }

        @media (max-width: 575px) {
          text-overflow: ellipsis;
          white-space: nowrap;
          overflow: hidden;
          cursor: pointer;
          max-width: 15ch;
          font-size: var(--font-size-sm);
        }
      }

      .media-icon-wrap {
        border: var(--border-width-xs) solid var(--color-primary);
        width: 40px;
        height: 40px;
        border-radius: var(--border-radius-full);
        padding: var(--spacing-sm);

        .icon-wrap {
          fill: var(--color-primary);
          height: 21px;
          width: 21px;

          @media (max-width: 575px) {
            height: 15px;
            width: 15px;
            gap: var(--spacing-xs) !important;
          }
        }

        @media (max-width: 575px) {
          padding: var(--spacing-tiny);
          width: 25px;
          height: 25px;
        }
      }

      @media (max-width: 1250px) {
        padding: var(--spacing-base) var(--spacing-none) var(--spacing-none)
          var(--spacing-none) !important;
        gap: var(--spacing-xxs);
      }
    }

    .more-item-icon {
      line-height: 0px;
      display: none;

      .more-item {
        fill: var(--color-primary);
      }

      @media (max-width: 991px) {
        display: block;
      }
    }

    .icons-wrap {
      line-height: 0px;

      .eye-icon,
      .download-icon,
      .delete-icon {
        fill: var(--color-primary);
        height: 21px;
        width: 21px;

        @media (max-width: 575px) {
          height: 17px;
          width: 17px;
        }
      }

      @media (max-width: 991px) {
        display: none !important;
      }
    }

    // File Grid Container - Change Request style
    .file-grid-container {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: var(--spacing-md);

      .file-grid-item {
        border: var(--normal-sec-border);
        border-radius: var(--border-radius-sm);
        padding: var(--spacing-sm);
      }

      @media (max-width: 991px) {
        grid-template-columns: repeat(2, 1fr);
      }
      
      @media (max-width: 575px) {
        grid-template-columns: repeat(1, 1fr);
      }
    }

    .selected-files {
      display: flex;
      justify-content: space-between;
      align-items: center;
      max-width: 640px;
      margin-bottom: var(--spacing-sm);

      svg {
        width: 24px;
        height: 24px;
        cursor: pointer;
      }

      .file-name {
        width: calc(100% - 10px - 24px);
        
        p {
          word-break: break-all;
        }
      }
    }
    
    .selected-view-files {
      margin-bottom: var(--spacing-xxs) !important;

      svg {
        width: 20px;
        height: 20px;
        cursor: pointer;
      }
    }

    // File grid item with hover effects for cross icon
    .file-grid-item-with-hover {
      position: relative;

      // Cross icon - hidden by default
      .file-remove-icon {
        position: absolute;
        top: calc(-1 * var(--spacing-xs));
        right: calc(-1 * var(--spacing-xs));
        background-color: var(--color-danger);
        color: var(--text-color-white);
        border-radius: var(--border-radius-full);
        opacity: 0;
        transition: all 0.2s ease-out;
        z-index: 10;
        width: 24px;
        height: 24px;
        padding: 0;
        box-shadow: var(--box-shadow-xs);

        &:hover {
          background-color: var(--icon-bold-red-color);
          opacity: 1 !important;
          transform: scale(1.1);
          box-shadow: var(--box-shadow-md);
        }

        svg {
          width: 16px;
          height: 16px;
          color: var(--text-color-white);
        }
      }

      // Show cross icon on hover
      &:hover {
        .file-remove-icon {
          opacity: 1;
        }
      }

      // Show cross icon on mobile devices
      @media (max-width: 768px) {
        .file-remove-icon {
          opacity: 0.9;
        }
      }
    }
  }
}

// Preview content styles
.preview-content {
  width: 100%;
  height: auto;
  max-height: 100vh;
  overflow: hidden;

  .preview-video {
    width: 100%;
    height: auto;
    max-height: 100vh;
    object-fit: contain;
    overflow: hidden;
  }

  .preview-image {
    width: 100%;
    height: auto;
    max-height: 100vh;
    object-fit: contain;
    overflow: hidden;
  }

  .preview-error {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 20px;
    text-align: center;
    color: #666;

    p {
      margin: 10px 0;
      font-size: 14px;
    }

    button {
      margin-top: 10px;
      padding: 8px 16px;
      background-color: #007bff;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;

      &:hover {
        background-color: #0056b3;
      }
    }
  }

  .preview-iframe {
    width: 100%;
    height: auto;
    max-height: 100vh;
    border: none;
  }

  .preview-default {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 200px;
  }
}

// Fullscreen dialog specific styles
.attachment-preview-dialog {
  .MuiDialog-paperScrollPaper {
    .dialog-content {
      overflow: hidden !important;

      .preview-content {
        height: calc(100vh - 120px); // Subtract header height
        max-height: calc(100vh - 120px);

        .preview-image,
        .preview-video,
        .preview-iframe {
          height: 100%;
          max-height: 100%;
        }
      }
    }
  }
}
