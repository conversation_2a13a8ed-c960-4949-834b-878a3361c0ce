@import '@/app/_globals.scss';

// Only keep styles for .form-wrap (form view) and .ticket-summary-card (card view)

.form-wrap {
  .form-section {
    margin-bottom: var(--spacing-lg);
    &:last-child {
      margin-bottom: 0;
    }
  }
  .field-group {
    margin-bottom: var(--spacing-md);
    .assignee-select {
      width: 100%;
      max-width: 300px;
    }
    &:last-child {
      margin-bottom: 0;
    }
  }
  .field-row {
    align-items: flex-start;
    gap: var(--spacing-md);
    .field-label {
      min-width: 120px;
      font-weight: var(--font-weight-medium);
      color: var(--color-dark);
      margin-top: var(--spacing-xs);
    }
  }
  .follower-select-container {
    width: 100%;
    max-width: 300px;
  }
  .custom-select-wrap {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-lg);
    @media (max-width: 767px) {
      grid-template-columns: repeat(1, 1fr);
      gap: 0;
    }
  }
  .followers-wrap {
    gap: 50px;
    .followers-list-wrap {
      .followers-list {
        gap: var(--spacing-xxs);
        display: flex;
        flex-wrap: wrap;
        .follower-item {
          position: relative;
          border-radius: var(--border-radius-full);
          overflow: hidden;

          // Overlay effect on hover
          &::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.4);
            border-radius: var(--border-radius-full);
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: 1;
          }

          &:hover::before {
            opacity: 1;
          }

          .user-avatar-fallback {
            width: 32px;
            height: 32px;
            border-radius: var(--border-radius-full);
            background-color: var(--color-primary);
            color: var(--color-white);
            font-size: var(--font-size-xs);
            font-weight: var(--font-weight-medium);
            font-family: var(--font-family-primary);
            .avatar-text {
              font-size: var(--font-size-xs);
              font-weight: var(--font-weight-medium);
              color: var(--color-white);
            }
          }
          .close-icon-wrap {
            position: absolute;
            left: 51%;
            bottom: 43%;
            transform: translate(-50%, 50%);
            opacity: 0;
            z-index: 2;
            transition: opacity 0.3s ease;
            .close-icon {
              fill: var(--color-white);
              height: var(--icon-size-xs);
              width: var(--icon-size-xs);
            }
          }
          &:hover .close-icon-wrap {
            opacity: 1;
            .close-icon {
              fill: var(--color-white); // Keep white color instead of red
            }
          }
        }
      }
      .add-follower-wrap {
        opacity: 1;
        cursor: pointer;
        transition: all 0.3s ease;
        .add-btn {
          fill: var(--color-primary);
          height: var(--icon-size-xsm);
          width: var(--icon-size-xsm);
          transition: fill 0.3s ease;
        }
        .add-follow-text {
          color: var(--color-primary);
          font-size: var(--font-size-sm);
          font-family: var(--font-family-primary);
          transition: color 0.3s ease;
        }
        &:hover {
          .add-btn {
            fill: var(--color-primary);
          }
          .add-follow-text {
            color: var(--color-primary);
          }
        }
      }
      .add-follower-section {
        position: relative;
        display: flex;
        align-items: center;
      }
      .followers-menu {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        z-index: 1000;
        background: var(--color-white);
        border: 1px solid var(--border-color-light-gray);
        border-radius: var(--border-radius-md);
        box-shadow: var(--box-shadow-xs);
        margin-top: var(--spacing-xxs);
        .followers-search {
          .select__control {
            padding: 0px;
            border: none;
            box-shadow: none;
            .select__value-container {
              padding: 0px;
              .select__single-value {
                margin-left: var(--spacing-base);
                margin-top: var(--spacing-xxs);
              }
              .select__input-container {
                margin: 0px 0px 0px var(--spacing-base);
              }
              .select__placeholder {
                padding: var(--spacing-xxs) 0px 0px var(--spacing-base);
                font-family: var(--font-family-primary);
              }
            }
            .select__indicators {
              .select__indicator {
                svg {
                  color: var(--color-dark);
                }
              }
            }
          }
        }
      }
    }
  }
  .support-ticket-owner-img {
    width: 35px;
    height: 35px;
    .MuiAvatar-circular {
      width: 35px;
      height: 35px;
    }
  }
}
